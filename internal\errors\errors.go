package errors

import (
	"fmt"
	"net/http"
)

// APIError represents a structured API error with code, message, and details
type APIError struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Details interface{} `json:"details,omitempty"`
	HTTPStatus int     `json:"-"`
}

// Error implements the error interface
func (e *APIError) Error() string {
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// WithDetails adds details to the error
func (e *APIError) WithDetails(details interface{}) *APIError {
	e.Details = details
	return e
}

// Predefined error types based on design.md
var (
	// Validation errors
	ErrInvalidImageFormat = &APIError{
		Code:       "INVALID_IMAGE_FORMAT",
		Message:    "Invalid image format. Supported formats: JPEG, PNG, GIF",
		HTTPStatus: http.StatusBadRequest,
	}

	ErrFileTooLarge = &APIError{
		Code:       "FILE_TOO_LARGE",
		Message:    "File size exceeds maximum allowed limit",
		HTTPStatus: http.StatusBadRequest,
	}

	ErrInvalidRequest = &APIError{
		Code:       "INVALID_REQUEST",
		Message:    "Invalid request format or missing required fields",
		HTTPStatus: http.StatusBadRequest,
	}

	ErrValidationFailed = &APIError{
		Code:       "VALIDATION_FAILED",
		Message:    "Request validation failed",
		HTTPStatus: http.StatusBadRequest,
	}

	// Processing errors
	ErrProcessingFailed = &APIError{
		Code:       "PROCESSING_FAILED",
		Message:    "Image processing failed",
		HTTPStatus: http.StatusInternalServerError,
	}

	ErrQRGenerationFailed = &APIError{
		Code:       "QR_GENERATION_FAILED",
		Message:    "QR code generation failed",
		HTTPStatus: http.StatusInternalServerError,
	}

	ErrCompositionFailed = &APIError{
		Code:       "COMPOSITION_FAILED",
		Message:    "Image composition failed",
		HTTPStatus: http.StatusInternalServerError,
	}

	// Storage errors
	ErrStorageFailed = &APIError{
		Code:       "STORAGE_FAILED",
		Message:    "Storage operation failed",
		HTTPStatus: http.StatusInternalServerError,
	}

	ErrFileNotFound = &APIError{
		Code:       "FILE_NOT_FOUND",
		Message:    "Requested file not found",
		HTTPStatus: http.StatusNotFound,
	}

	ErrMetadataNotFound = &APIError{
		Code:       "METADATA_NOT_FOUND",
		Message:    "File metadata not found",
		HTTPStatus: http.StatusNotFound,
	}

	// System errors
	ErrInternalServer = &APIError{
		Code:       "INTERNAL_SERVER_ERROR",
		Message:    "Internal server error occurred",
		HTTPStatus: http.StatusInternalServerError,
	}

	ErrServiceUnavailable = &APIError{
		Code:       "SERVICE_UNAVAILABLE",
		Message:    "Service temporarily unavailable",
		HTTPStatus: http.StatusServiceUnavailable,
	}

	ErrTimeout = &APIError{
		Code:       "TIMEOUT",
		Message:    "Request timeout",
		HTTPStatus: http.StatusRequestTimeout,
	}

	ErrRateLimitExceeded = &APIError{
		Code:       "RATE_LIMIT_EXCEEDED",
		Message:    "Rate limit exceeded",
		HTTPStatus: http.StatusTooManyRequests,
	}

	// Resource errors
	ErrInsufficientMemory = &APIError{
		Code:       "INSUFFICIENT_MEMORY",
		Message:    "Insufficient memory to process request",
		HTTPStatus: http.StatusInsufficientStorage,
	}

	ErrResourceExhausted = &APIError{
		Code:       "RESOURCE_EXHAUSTED",
		Message:    "System resources exhausted",
		HTTPStatus: http.StatusServiceUnavailable,
	}
)

// NewAPIError creates a new APIError with the given parameters
func NewAPIError(code, message string, httpStatus int) *APIError {
	return &APIError{
		Code:       code,
		Message:    message,
		HTTPStatus: httpStatus,
	}
}

// WrapError wraps a standard error into an APIError
func WrapError(err error, apiErr *APIError) *APIError {
	if err == nil {
		return nil
	}
	
	newErr := *apiErr // Copy the APIError
	newErr.Details = err.Error()
	return &newErr
}

// IsAPIError checks if an error is an APIError
func IsAPIError(err error) (*APIError, bool) {
	if apiErr, ok := err.(*APIError); ok {
		return apiErr, true
	}
	return nil, false
}