package worker

import (
	"bytes"
	"sync"
	"sync/atomic"
	"time"

	"qr-background-api/internal/interfaces"
)

// buffer<PERSON><PERSON> implements the BufferPool interface with thread-safe operations
type bufferPool struct {
	qrBuffers    chan *bytes.Buffer
	imageBuffers chan *bytes.Buffer
	maxBufferSize int
	
	// Statistics (atomic counters for thread safety)
	qrBuffersInUse     int64
	imageBuffersInUse int64
	totalAllocated     int64
	totalReused        int64
	
	// Cleanup management
	lastCleanup time.Time
	cleanupMu   sync.RWMutex
}

// NewBufferPool creates a new buffer pool with specified configuration
func NewBufferPool(qrPoolSize, imagePoolSize, maxBufferSize int) interfaces.BufferPool {
	bp := &bufferPool{
		qrBuffers:     make(chan *bytes.Buffer, qrPoolSize),
		imageBuffers:  make(chan *bytes.Buffer, imagePoolSize),
		maxBufferSize: maxBufferSize,
		lastCleanup:   time.Now(),
	}
	
	// Pre-allocate buffers for optimal performance
	for i := 0; i < qrPoolSize; i++ {
		bp.qrBuffers <- bytes.NewBuffer(make([]byte, 0, 1024)) // 1KB initial capacity for QR
	}
	
	for i := 0; i < imagePoolSize; i++ {
		bp.imageBuffers <- bytes.NewBuffer(make([]byte, 0, 64*1024)) // 64KB initial capacity for images
	}
	
	atomic.AddInt64(&bp.totalAllocated, int64(qrPoolSize+imagePoolSize))
	
	return bp
}

// GetQRBuffer returns a buffer for QR code generation
func (bp *bufferPool) GetQRBuffer() *bytes.Buffer {
	select {
	case buf := <-bp.qrBuffers:
		// Reset buffer for reuse
		buf.Reset()
		atomic.AddInt64(&bp.qrBuffersInUse, 1)
		atomic.AddInt64(&bp.totalReused, 1)
		return buf
	default:
		// Pool is empty, create new buffer
		atomic.AddInt64(&bp.qrBuffersInUse, 1)
		atomic.AddInt64(&bp.totalAllocated, 1)
		return bytes.NewBuffer(make([]byte, 0, 1024))
	}
}

// GetImageBuffer returns a buffer for image processing
func (bp *bufferPool) GetImageBuffer() *bytes.Buffer {
	select {
	case buf := <-bp.imageBuffers:
		// Reset buffer for reuse
		buf.Reset()
		atomic.AddInt64(&bp.imageBuffersInUse, 1)
		atomic.AddInt64(&bp.totalReused, 1)
		return buf
	default:
		// Pool is empty, create new buffer
		atomic.AddInt64(&bp.imageBuffersInUse, 1)
		atomic.AddInt64(&bp.totalAllocated, 1)
		return bytes.NewBuffer(make([]byte, 0, 64*1024))
	}
}

// ReturnQRBuffer returns a QR buffer to the pool for reuse
func (bp *bufferPool) ReturnQRBuffer(buf *bytes.Buffer) {
	if buf == nil {
		return
	}
	
	atomic.AddInt64(&bp.qrBuffersInUse, -1)
	
	// Check if buffer is too large and should be discarded
	if buf.Cap() > bp.maxBufferSize {
		return
	}
	
	// Try to return buffer to pool
	select {
	case bp.qrBuffers <- buf:
		// Successfully returned to pool
	default:
		// Pool is full, discard buffer
	}
}

// ReturnImageBuffer returns an image buffer to the pool for reuse
func (bp *bufferPool) ReturnImageBuffer(buf *bytes.Buffer) {
	if buf == nil {
		return
	}
	
	atomic.AddInt64(&bp.imageBuffersInUse, -1)
	
	// Check if buffer is too large and should be discarded
	if buf.Cap() > bp.maxBufferSize {
		return
	}
	
	// Try to return buffer to pool
	select {
	case bp.imageBuffers <- buf:
		// Successfully returned to pool
	default:
		// Pool is full, discard buffer
	}
}

// GetStats returns buffer pool statistics
func (bp *bufferPool) GetStats() interfaces.BufferPoolStats {
	return interfaces.BufferPoolStats{
		QRBuffersInUse:        int(atomic.LoadInt64(&bp.qrBuffersInUse)),
		QRBuffersAvailable:    len(bp.qrBuffers),
		ImageBuffersInUse:     int(atomic.LoadInt64(&bp.imageBuffersInUse)),
		ImageBuffersAvailable: len(bp.imageBuffers),
		TotalAllocated:        atomic.LoadInt64(&bp.totalAllocated),
		TotalReused:           atomic.LoadInt64(&bp.totalReused),
	}
}

// Cleanup removes unused buffers and frees memory
func (bp *bufferPool) Cleanup() {
	bp.cleanupMu.Lock()
	defer bp.cleanupMu.Unlock()
	
	// Only cleanup if enough time has passed
	if time.Since(bp.lastCleanup) < 5*time.Minute {
		return
	}
	
	// Drain and recreate channels to remove oversized buffers
	qrCount := len(bp.qrBuffers)
	imageCount := len(bp.imageBuffers)
	
	// Drain existing buffers
	for len(bp.qrBuffers) > 0 {
		<-bp.qrBuffers
	}
	for len(bp.imageBuffers) > 0 {
		<-bp.imageBuffers
	}
	
	// Recreate with smaller, fresh buffers
	for i := 0; i < qrCount/2; i++ {
		bp.qrBuffers <- bytes.NewBuffer(make([]byte, 0, 1024))
	}
	for i := 0; i < imageCount/2; i++ {
		bp.imageBuffers <- bytes.NewBuffer(make([]byte, 0, 64*1024))
	}
	
	bp.lastCleanup = time.Now()
}