# Upload Endpoint

Upload background images that can be used for QR code generation.

## Endpoint Details

- **URL**: `/upload`
- **Method**: `POST`
- **Content-Type**: `multipart/form-data`
- **Timeout**: 60 seconds

## Request

### Headers
```http
Content-Type: multipart/form-data
```

### Body Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `file` | File | Yes | Image file to upload |

### Supported File Formats
- **JPEG** (`.jpg`, `.jpeg`)
- **PNG** (`.png`)
- **GIF** (`.gif`)

### File Size Limits
- **Maximum**: 10MB (configurable)
- **Minimum**: No minimum limit

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "image_path": "images/347/abc123def456.jpg"
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Always `true` for successful uploads |
| `message` | string | Success message |
| `image_path` | string | Path to uploaded image for use in QR generation |

## Error Responses

### 400 Bad Request - Invalid Image Format
```json
{
  "success": false,
  "error": {
    "code": "INVALID_IMAGE_FORMAT",
    "message": "Invalid image format. Supported formats: JPEG, PNG, GIF",
    "details": {
      "provided_format": "image/bmp",
      "supported_formats": ["image/jpeg", "image/png", "image/gif"]
    }
  }
}
```

### 400 Bad Request - File Too Large
```json
{
  "success": false,
  "error": {
    "code": "FILE_TOO_LARGE",
    "message": "File size exceeds maximum allowed limit",
    "details": {
      "file_size": 15728640,
      "max_size": 10485760,
      "size_mb": 15.0,
      "max_mb": 10.0
    }
  }
}
```

### 400 Bad Request - Invalid Request
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid request format or missing required fields",
    "details": "multipart: no multipart boundary param in Content-Type"
  }
}
```

### 405 Method Not Allowed
```json
{
  "success": false,
  "error": {
    "code": "METHOD_NOT_ALLOWED",
    "message": "Method not allowed",
    "details": "Only POST method is supported"
  }
}
```

### 500 Internal Server Error - Storage Failed
```json
{
  "success": false,
  "error": {
    "code": "STORAGE_FAILED",
    "message": "Failed to store uploaded file",
    "details": "disk space insufficient"
  }
}
```

## Examples

### cURL Example
```bash
curl -X POST http://localhost:8080/upload \
  -F "file=@/path/to/background.jpg" \
  -H "Accept: application/json"
```

### JavaScript Example (Browser)
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);

fetch('http://localhost:8080/upload', {
  method: 'POST',
  body: formData
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Upload successful:', data.image_path);
  } else {
    console.error('Upload failed:', data.error);
  }
});
```

### Python Example
```python
import requests

url = 'http://localhost:8080/upload'
files = {'file': open('background.jpg', 'rb')}

response = requests.post(url, files=files)
data = response.json()

if data['success']:
    print(f"Upload successful: {data['image_path']}")
else:
    print(f"Upload failed: {data['error']['message']}")
```

## Implementation Details

### File Processing
1. **Validation**: Content-Type header is checked against allowed formats
2. **Size Check**: File size is validated against configured limits
3. **Unique ID**: A unique identifier is generated for the file
4. **Storage**: File is saved to local storage with organized folder structure
5. **Metadata**: Metadata file is created with upload information

### File Organization
- Files are organized in numbered folders (e.g., `images/347/`)
- Each file gets a unique filename with original extension
- Metadata is stored in `.meta` files alongside images

### Performance Considerations
- Files are streamed during upload to minimize memory usage
- Large files are processed in chunks
- Concurrent uploads are supported
- Automatic cleanup of temporary files on errors

## Validation Rules

### File Format Validation
- Content-Type header must match supported formats
- File extension is not used for validation (security)
- Binary content is not analyzed (performance)

### Size Validation
- Checked during multipart parsing
- Configurable via `storage.max_file_size` in config
- Default limit: 10MB

## Next Steps

After successful upload, use the returned `image_path` in the [QR Generation endpoint](./qr-generation.md) to create QR codes with the uploaded background.
