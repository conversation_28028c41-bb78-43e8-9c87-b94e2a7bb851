package compositor

import (
	"bytes"
	"image"
	"image/color"
	"image/png"
	"testing"
)

// createTestImage creates a simple test image with specified dimensions and color
func createTestImage(width, height int, c color.Color) image.Image {
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			img.Set(x, y, c)
		}
	}
	return img
}

func TestNewCompositor(t *testing.T) {
	compositor := NewCompositor()
	if compositor == nil {
		t.Error("NewCompositor should return a non-nil compositor")
	}
}

func TestComposite_ValidPositioning(t *testing.T) {
	compositor := NewCompositor()
	
	// Create test images
	background := createTestImage(200, 200, color.RGBA{255, 255, 255, 255}) // White background
	qr := createTestImage(50, 50, color.RGBA{0, 0, 0, 255})                 // Black QR code
	
	// Test valid positioning
	result, err := compositor.Composite(background, qr, 10, 10)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("Composite should succeed with valid positioning: %v", err)
	}
	
	if result == nil {
		t.Error("Composite should return a non-nil image")
	}
	
	// Check that the result has the same bounds as the background
	if result.Bounds() != background.Bounds() {
		t.Error("Composite result should have the same bounds as background")
	}
}

func TestComposite_InvalidPositioning(t *testing.T) {
	compositor := NewCompositor()
	
	// Create test images
	background := createTestImage(100, 100, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(50, 50, color.RGBA{0, 0, 0, 255})
	
	tests := []struct {
		name string
		x, y int
	}{
		{"Negative X position", -1, 10},
		{"Negative Y position", 10, -1},
		{"QR extends beyond width", 60, 10},  // 60 + 50 = 110 > 100
		{"QR extends beyond height", 10, 60}, // 60 + 50 = 110 > 100
		{"QR extends beyond both dimensions", 80, 80},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := compositor.Composite(background, qr, tt.x, tt.y)
			if err == nil {
				t.Errorf("Composite should fail with invalid positioning: x=%d, y=%d", tt.x, tt.y)
			}
		})
	}
}

func TestComposite_EdgePositioning(t *testing.T) {
	compositor := NewCompositor()
	
	// Create test images
	background := createTestImage(100, 100, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(50, 50, color.RGBA{0, 0, 0, 255})
	
	tests := []struct {
		name string
		x, y int
	}{
		{"Top-left corner", 0, 0},
		{"Top-right corner", 50, 0},
		{"Bottom-left corner", 0, 50},
		{"Bottom-right corner", 50, 50},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := compositor.Composite(background, qr, tt.x, tt.y)
			if err != nil {
				t.Errorf("Composite should succeed with edge positioning: %v", err)
			}
			if result == nil {
				t.Error("Composite should return a non-nil image")
			}
		})
	}
}

func TestCompositeToBuffer_JPEG(t *testing.T) {
	compositor := NewCompositor()
	
	// Create test images
	background := createTestImage(100, 100, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(30, 30, color.RGBA{0, 0, 0, 255})
	
	buf := &bytes.Buffer{}
	err := compositor.CompositeToBuffer(background, qr, 10, 10, buf, "jpeg")
	if err != nil {
		t.Errorf("CompositeToBuffer should succeed with JPEG format: %v", err)
	}
	
	if buf.Len() == 0 {
		t.Error("Buffer should contain encoded image data")
	}
}

func TestCompositeToBuffer_PNG(t *testing.T) {
	compositor := NewCompositor()
	
	// Create test images
	background := createTestImage(100, 100, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(30, 30, color.RGBA{0, 0, 0, 255})
	
	buf := &bytes.Buffer{}
	err := compositor.CompositeToBuffer(background, qr, 10, 10, buf, "png")
	if err != nil {
		t.Errorf("CompositeToBuffer should succeed with PNG format: %v", err)
	}
	
	if buf.Len() == 0 {
		t.Error("Buffer should contain encoded image data")
	}
	
	// Verify that the buffer contains valid PNG data
	_, err = png.Decode(bytes.NewReader(buf.Bytes()))
	if err != nil {
		t.Errorf("Buffer should contain valid PNG data: %v", err)
	}
}

func TestCompositeToBuffer_WebP(t *testing.T) {
	compositor := NewCompositor()
	
	// Create test images
	background := createTestImage(100, 100, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(30, 30, color.RGBA{0, 0, 0, 255})
	
	buf := &bytes.Buffer{}
	err := compositor.CompositeToBuffer(background, qr, 10, 10, buf, "webp")
	if err != nil {
		t.Errorf("CompositeToBuffer should succeed with WebP format (fallback to PNG): %v", err)
	}
	
	if buf.Len() == 0 {
		t.Error("Buffer should contain encoded image data")
	}
}

func TestCompositeToBuffer_UnsupportedFormat(t *testing.T) {
	compositor := NewCompositor()
	
	// Create test images
	background := createTestImage(100, 100, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(30, 30, color.RGBA{0, 0, 0, 255})
	
	buf := &bytes.Buffer{}
	err := compositor.CompositeToBuffer(background, qr, 10, 10, buf, "bmp")
	if err == nil {
		t.Error("CompositeToBuffer should fail with unsupported format")
	}
}

func TestCompositeToBuffer_InvalidPositioning(t *testing.T) {
	compositor := NewCompositor()
	
	// Create test images
	background := createTestImage(100, 100, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(50, 50, color.RGBA{0, 0, 0, 255})
	
	buf := &bytes.Buffer{}
	err := compositor.CompositeToBuffer(background, qr, 60, 60, buf, "png")
	if err == nil {
		t.Error("CompositeToBuffer should fail with invalid positioning")
	}
}

func TestValidatePositioning(t *testing.T) {
	compositor := NewCompositor()
	
	// Create test images
	background := createTestImage(100, 100, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(30, 30, color.RGBA{0, 0, 0, 255})
	
	tests := []struct {
		name      string
		x, y      int
		shouldErr bool
	}{
		{"Valid position", 10, 10, false},
		{"Edge position", 70, 70, false},
		{"Negative X", -1, 10, true},
		{"Negative Y", 10, -1, true},
		{"Exceeds width", 71, 10, true},
		{"Exceeds height", 10, 71, true},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := compositor.validatePositioning(background, qr, tt.x, tt.y)
			if tt.shouldErr && err == nil {
				t.Errorf("validatePositioning should return error for position (%d, %d)", tt.x, tt.y)
			}
			if !tt.shouldErr && err != nil {
				t.Errorf("validatePositioning should not return error for position (%d, %d): %v", tt.x, tt.y, err)
			}
		})
	}
}

func TestEncodeToBuffer_AllFormats(t *testing.T) {
	compositor := NewCompositor()
	img := createTestImage(50, 50, color.RGBA{128, 128, 128, 255})
	
	formats := []string{"jpeg", "jpg", "png", "webp"}
	
	for _, format := range formats {
		t.Run(format, func(t *testing.T) {
			buf := &bytes.Buffer{}
			err := compositor.encodeToBuffer(img, buf, format)
			if err != nil {
				t.Errorf("encodeToBuffer should succeed with format %s: %v", format, err)
			}
			if buf.Len() == 0 {
				t.Errorf("Buffer should contain data for format %s", format)
			}
		})
	}
}

func BenchmarkComposite(b *testing.B) {
	compositor := NewCompositor()
	background := createTestImage(500, 500, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(100, 100, color.RGBA{0, 0, 0, 255})
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := compositor.Composite(background, qr, 50, 50)
		if err != nil {
			b.Fatalf("Composite failed: %v", err)
		}
	}
}

func BenchmarkCompositeToBuffer(b *testing.B) {
	compositor := NewCompositor()
	background := createTestImage(500, 500, color.RGBA{255, 255, 255, 255})
	qr := createTestImage(100, 100, color.RGBA{0, 0, 0, 255})
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		buf := &bytes.Buffer{}
		err := compositor.CompositeToBuffer(background, qr, 50, 50, buf, "png")
		if err != nil {
			b.Fatalf("CompositeToBuffer failed: %v", err)
		}
	}
}