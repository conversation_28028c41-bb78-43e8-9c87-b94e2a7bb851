package errors

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestNewAPIError(t *testing.T) {
	tests := []struct {
		name       string
		code       string
		message    string
		httpStatus int
		expected   APIError
	}{
		{
			name:       "basic error",
			code:       "BAD_REQUEST",
			message:    "Bad Request",
			httpStatus: 400,
			expected: APIError{
				Code:       "BAD_REQUEST",
				Message:    "Bad Request",
				HTTPStatus: 400,
			},
		},
		{
			name:       "server error",
			code:       "INTERNAL_SERVER_ERROR",
			message:    "Internal Server Error",
			httpStatus: 500,
			expected: APIError{
				Code:       "INTERNAL_SERVER_ERROR",
				Message:    "Internal Server Error",
				HTTPStatus: 500,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := NewAPIError(tt.code, tt.message, tt.httpStatus)
			if result.Code != tt.expected.Code {
				t.Errorf("Expected code %s, got %s", tt.expected.Code, result.Code)
			}
			if result.Message != tt.expected.Message {
				t.Errorf("Expected message %s, got %s", tt.expected.Message, result.Message)
			}
			if result.HTTPStatus != tt.expected.HTTPStatus {
				t.Errorf("Expected HTTPStatus %d, got %d", tt.expected.HTTPStatus, result.HTTPStatus)
			}
		})
	}
}

func TestAPIError_WithDetails(t *testing.T) {
	originalError := NewAPIError("BAD_REQUEST", "Bad Request", 400)
	newDetails := "Updated details with more information"

	updatedError := originalError.WithDetails(newDetails)

	if updatedError.Code != originalError.Code {
		t.Errorf("Expected code %s, got %s", originalError.Code, updatedError.Code)
	}
	if updatedError.Message != originalError.Message {
		t.Errorf("Expected message %s, got %s", originalError.Message, updatedError.Message)
	}
	if updatedError.Details != newDetails {
		t.Errorf("Expected details %v, got %v", newDetails, updatedError.Details)
	}

	// Ensure original error is modified (WithDetails modifies the original)
	if originalError.Details != newDetails {
		t.Errorf("Original error details should be modified by WithDetails")
	}
}

func TestAPIError_Error(t *testing.T) {
	apiError := NewAPIError("NOT_FOUND", "Not Found", 404)
	expected := "[NOT_FOUND] Not Found"

	result := apiError.Error()
	if result != expected {
		t.Errorf("Expected error string %s, got %s", expected, result)
	}
}

func TestWriteErrorResponse(t *testing.T) {
	tests := []struct {
		name           string
		apiError       *APIError
		expectedStatus int
	}{
		{
			name:           "bad request error",
			apiError:       NewAPIError("BAD_REQUEST", "Bad Request", http.StatusBadRequest).WithDetails("Invalid JSON"),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "internal server error",
			apiError:       NewAPIError("INTERNAL_SERVER_ERROR", "Internal Server Error", http.StatusInternalServerError).WithDetails("Database error"),
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			r := httptest.NewRequest(http.MethodGet, "/test", nil)

			WriteErrorResponse(w, r, tt.apiError)

			// Check status code
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status code %d, got %d", tt.expectedStatus, w.Code)
			}

			// Check content type
			contentType := w.Header().Get("Content-Type")
			if contentType != "application/json" {
				t.Errorf("Expected content type application/json, got %s", contentType)
			}

			// Check response body
			var response ErrorResponse
			if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
				t.Fatalf("Failed to unmarshal response: %v", err)
			}

			if response.Success != false {
				t.Errorf("Expected success false, got %v", response.Success)
			}
			if response.Error != tt.apiError.Message {
				t.Errorf("Expected error %s, got %s", tt.apiError.Message, response.Error)
			}
			if response.Code != tt.apiError.Code {
				t.Errorf("Expected code %s, got %s", tt.apiError.Code, response.Code)
			}
			if response.Details != tt.apiError.Details {
				t.Errorf("Expected details %v, got %v", tt.apiError.Details, response.Details)
			}
		})
	}
}

func TestWriteErrorResponse_InvalidJSON(t *testing.T) {
	// Test case where JSON encoding might fail (though unlikely with our simple struct)
	w := httptest.NewRecorder()
	r := httptest.NewRequest(http.MethodGet, "/test", nil)
	apiError := NewAPIError("TEST_ERROR", "Test Error", http.StatusInternalServerError).WithDetails("Test Details")

	WriteErrorResponse(w, r, apiError)

	// Should still work normally
	if w.Code != http.StatusInternalServerError {
		t.Errorf("Expected status code %d, got %d", http.StatusInternalServerError, w.Code)
	}

	contentType := w.Header().Get("Content-Type")
	if contentType != "application/json" {
		t.Errorf("Expected content type application/json, got %s", contentType)
	}
}

func TestErrorResponse_JSON(t *testing.T) {
	errorResp := ErrorResponse{
		Success: false,
		Error:   "Test Error",
		Code:    "TEST_ERROR",
		Details: "Test Details",
	}

	data, err := json.Marshal(errorResp)
	if err != nil {
		t.Fatalf("Failed to marshal ErrorResponse: %v", err)
	}

	var unmarshaled ErrorResponse
	if err := json.Unmarshal(data, &unmarshaled); err != nil {
		t.Fatalf("Failed to unmarshal ErrorResponse: %v", err)
	}

	if unmarshaled.Success != errorResp.Success {
		t.Errorf("Expected success %v, got %v", errorResp.Success, unmarshaled.Success)
	}
	if unmarshaled.Error != errorResp.Error {
		t.Errorf("Expected error %s, got %s", errorResp.Error, unmarshaled.Error)
	}
	if unmarshaled.Code != errorResp.Code {
		t.Errorf("Expected code %s, got %s", errorResp.Code, unmarshaled.Code)
	}
	if unmarshaled.Details != errorResp.Details {
		t.Errorf("Expected details %v, got %v", errorResp.Details, unmarshaled.Details)
	}
}

func TestRecoverFromPanicWithContext(t *testing.T) {
	// Test no panic case - function should return nil when no panic occurs
	t.Run("no panic", func(t *testing.T) {
		result := RecoverFromPanicWithContext("test_op", "req-123")
		if result != nil {
			t.Errorf("Expected no error, got %v", result)
		}
	})

	// Note: Testing actual panic recovery is complex and is better tested
	// through integration tests with middleware that use these functions.
	// The panic recovery functionality is tested in middleware tests.
}

func TestAggregateValidationErrors(t *testing.T) {
	tests := []struct {
		name   string
		errors []ValidationError
		expect int
	}{
		{
			name:   "empty errors",
			errors: []ValidationError{},
			expect: 0,
		},
		{
			name: "single error",
			errors: []ValidationError{
				{Field: "name", Message: "required"},
			},
			expect: 1,
		},
		{
			name: "multiple errors",
			errors: []ValidationError{
				{Field: "name", Message: "required"},
				{Field: "email", Message: "invalid format"},
				{Field: "age", Message: "must be positive"},
			},
			expect: 3,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := AggregateValidationErrors(tt.errors)

			if len(result) != tt.expect {
				t.Errorf("Expected %d errors, got %d", tt.expect, len(result))
			}

			for i, err := range tt.errors {
				if i < len(result) {
					if result[i].Field != err.Field {
						t.Errorf("Expected field %s, got %s", err.Field, result[i].Field)
					}
					if result[i].Message != err.Message {
						t.Errorf("Expected message %s, got %s", err.Message, result[i].Message)
					}
				}
			}
		})
	}
}

func TestValidateAndAggregateErrors(t *testing.T) {
	tests := []struct {
		name        string
		validations map[string]func() error
		expectCount int
	}{
		{
			name:        "no validations",
			validations: map[string]func() error{},
			expectCount: 0,
		},
		{
			name: "all pass",
			validations: map[string]func() error{
				"field1": func() error { return nil },
				"field2": func() error { return nil },
			},
			expectCount: 0,
		},
		{
			name: "some fail",
			validations: map[string]func() error{
				"field1": func() error { return nil },
				"field2": func() error { return fmt.Errorf("validation failed") },
				"field3": func() error { return fmt.Errorf("another error") },
			},
			expectCount: 2,
		},
		{
			name: "all fail",
			validations: map[string]func() error{
				"field1": func() error { return fmt.Errorf("error 1") },
				"field2": func() error { return fmt.Errorf("error 2") },
			},
			expectCount: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ValidateAndAggregateErrors(tt.validations)

			if len(result) != tt.expectCount {
				t.Errorf("Expected %d errors, got %d", tt.expectCount, len(result))
			}
		})
	}
}

func TestWithRequestContext(t *testing.T) {
	tests := []struct {
		name      string
		err       *APIError
		requestID string
		method    string
		path      string
		expectNil bool
	}{
		{
			name:      "nil error",
			err:       nil,
			requestID: "req-123",
			method:    "POST",
			path:      "/test",
			expectNil: true,
		},
		{
			name:      "error without existing details",
			err:       NewAPIError("TEST_ERROR", "Test error", 400),
			requestID: "req-123",
			method:    "POST",
			path:      "/test",
			expectNil: false,
		},
		{
			name: "error with existing details",
			err: NewAPIError("TEST_ERROR", "Test error", 400).WithDetails(map[string]interface{}{
				"existing": "value",
			}),
			requestID: "req-123",
			method:    "POST",
			path:      "/test",
			expectNil: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := WithRequestContext(tt.err, tt.requestID, tt.method, tt.path)

			if tt.expectNil {
				if result != nil {
					t.Errorf("Expected nil result, got %v", result)
				}
				return
			}

			if result == nil {
				t.Error("Expected non-nil result")
				return
			}

			details, ok := result.Details.(map[string]interface{})
			if !ok {
				t.Error("Expected details to be map[string]interface{}")
				return
			}

			if details["request_id"] != tt.requestID {
				t.Errorf("Expected request_id %s, got %v", tt.requestID, details["request_id"])
			}

			if details["method"] != tt.method {
				t.Errorf("Expected method %s, got %v", tt.method, details["method"])
			}

			if details["path"] != tt.path {
				t.Errorf("Expected path %s, got %v", tt.path, details["path"])
			}
		})
	}
}

func TestGetErrorSeverity(t *testing.T) {
	tests := []struct {
		name     string
		err      *APIError
		expected string
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: "info",
		},
		{
			name:     "400 error",
			err:      NewAPIError("BAD_REQUEST", "Bad request", 400),
			expected: "warn",
		},
		{
			name:     "404 error",
			err:      NewAPIError("NOT_FOUND", "Not found", 404),
			expected: "warn",
		},
		{
			name:     "500 error",
			err:      NewAPIError("INTERNAL_ERROR", "Internal error", 500),
			expected: "error",
		},
		{
			name:     "503 error",
			err:      NewAPIError("SERVICE_UNAVAILABLE", "Service unavailable", 503),
			expected: "error",
		},
		{
			name:     "200 status",
			err:      NewAPIError("SUCCESS", "Success", 200),
			expected: "info",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetErrorSeverity(tt.err)

			if result != tt.expected {
				t.Errorf("Expected severity %s, got %s", tt.expected, result)
			}
		})
	}
}

func TestIsRetryableError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil error",
			err:      nil,
			expected: false,
		},
		{
			name:     "timeout error",
			err:      NewAPIError("TIMEOUT", "Request timeout", 408),
			expected: true,
		},
		{
			name:     "server error",
			err:      NewAPIError("INTERNAL_ERROR", "Internal error", 500),
			expected: true,
		},
		{
			name:     "bad gateway error",
			err:      NewAPIError("BAD_GATEWAY", "Bad gateway", 502),
			expected: true,
		},
		{
			name:     "client error",
			err:      NewAPIError("BAD_REQUEST", "Bad request", 400),
			expected: false,
		},
		{
			name:     "not found error",
			err:      NewAPIError("NOT_FOUND", "Not found", 404),
			expected: false,
		},
		{
			name:     "generic timeout error",
			err:      fmt.Errorf("connection timeout"),
			expected: true,
		},
		{
			name:     "connection refused error",
			err:      fmt.Errorf("connection refused"),
			expected: true,
		},
		{
			name:     "generic client error",
			err:      fmt.Errorf("invalid input"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsRetryableError(tt.err)

			if result != tt.expected {
				t.Errorf("Expected retryable %v, got %v", tt.expected, result)
			}
		})
	}
}
