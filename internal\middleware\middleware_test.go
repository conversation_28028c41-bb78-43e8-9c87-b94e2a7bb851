package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"qr-background-api/internal/logging"
)

func TestLoggingMiddleware(t *testing.T) {
	// Initialize logger for testing
	var buf bytes.Buffer
	config := logging.Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}
	logging.InitGlobalLogger(config)
	logger := logging.GetLogger()
	logger.Logger.SetOutput(&buf)

	// Create test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	})

	// Wrap with logging middleware
	handler := LoggingMiddleware(testHandler)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("User-Agent", "test-agent")
	req.RemoteAddr = "192.168.1.1:12345"
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}
	if w.Body.String() != "test response" {
		t.Errorf("Expected 'test response', got %s", w.Body.String())
	}

	// Check logs
	logOutput := buf.String()
	if !strings.Contains(logOutput, "Request started") {
		t.Error("Expected 'Request started' log entry")
	}
	if !strings.Contains(logOutput, "Request completed") {
		t.Error("Expected 'Request completed' log entry")
	}
	if !strings.Contains(logOutput, "/test") {
		t.Error("Expected request path in logs")
	}
	if !strings.Contains(logOutput, "GET") {
		t.Error("Expected request method in logs")
	}
}

func TestTimeoutMiddleware(t *testing.T) {
	tests := []struct {
		name           string
		timeout        time.Duration
		handlerDelay   time.Duration
		expectedStatus int
		expectTimeout  bool
	}{
		{
			name:           "request completes within timeout",
			timeout:        100 * time.Millisecond,
			handlerDelay:   10 * time.Millisecond,
			expectedStatus: http.StatusOK,
			expectTimeout:  false,
		},
		{
			name:           "request times out",
			timeout:        50 * time.Millisecond,
			handlerDelay:   100 * time.Millisecond,
			expectedStatus: http.StatusRequestTimeout,
			expectTimeout:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test handler with delay
			testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				time.Sleep(tt.handlerDelay)
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("success"))
			})

			// Wrap with timeout middleware
			handler := TimeoutMiddleware(testHandler, tt.timeout)

			// Create test request
			req := httptest.NewRequest("GET", "/test", nil)
			w := httptest.NewRecorder()

			// Execute request
			handler.ServeHTTP(w, req)

			// Check response
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			if tt.expectTimeout {
				// Check timeout response
				var response map[string]interface{}
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to parse timeout response: %v", err)
				}
				if response["success"] != false {
					t.Error("Expected success=false in timeout response")
				}
				if !strings.Contains(response["error"].(string), "timeout") {
					t.Error("Expected timeout error message")
				}
			} else {
				// Check successful response
				if w.Body.String() != "success" {
					t.Errorf("Expected 'success', got %s", w.Body.String())
				}
			}
		})
	}
}

func TestRecoveryMiddleware(t *testing.T) {
	// Initialize logger for testing
	var buf bytes.Buffer
	config := logging.Config{
		Level:      "error",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}
	logging.InitGlobalLogger(config)
	logger := logging.GetLogger()
	logger.Logger.SetOutput(&buf)

	tests := []struct {
		name           string
		handler        http.HandlerFunc
		expectedStatus int
		expectPanic    bool
	}{
		{
			name: "normal handler execution",
			handler: func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("normal response"))
			},
			expectedStatus: http.StatusOK,
			expectPanic:    false,
		},
		{
			name: "handler panics",
			handler: func(w http.ResponseWriter, r *http.Request) {
				panic("test panic")
			},
			expectedStatus: http.StatusInternalServerError,
			expectPanic:    true,
		},
		{
			name: "handler panics with nil",
			handler: func(w http.ResponseWriter, r *http.Request) {
				panic(nil)
			},
			expectedStatus: http.StatusInternalServerError,
			expectPanic:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clear buffer
			buf.Reset()

			// Wrap with recovery middleware
			handler := RecoveryMiddleware(tt.handler)

			// Create test request
			req := httptest.NewRequest("GET", "/test", nil)
			w := httptest.NewRecorder()

			// Execute request (should not panic)
			handler.ServeHTTP(w, req)

			// Check response status
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			if tt.expectPanic {
				// Check error response
				var response map[string]interface{}
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to parse error response: %v", err)
				}
				if response["success"] != false {
					t.Error("Expected success=false in panic response")
				}
				if !strings.Contains(response["error"].(string), "Internal server error") {
					t.Error("Expected internal server error message")
				}

				// Check that panic was logged
				logOutput := buf.String()
				if !strings.Contains(logOutput, "Panic recovered") {
					t.Error("Expected panic recovery log entry")
				}
			} else {
				// Check normal response
				if w.Body.String() != "normal response" {
					t.Errorf("Expected 'normal response', got %s", w.Body.String())
				}
			}
		})
	}
}

func TestRequestSizeLimitMiddleware(t *testing.T) {
	tests := []struct {
		name           string
		maxSize        int64
		requestBody    string
		expectedStatus int
		expectError    bool
	}{
		{
			name:           "request within size limit",
			maxSize:        100,
			requestBody:    "small body",
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
		{
			name:           "request exceeds size limit",
			maxSize:        10,
			requestBody:    "this is a very long request body that exceeds the limit",
			expectedStatus: http.StatusRequestEntityTooLarge,
			expectError:    true,
		},
		{
			name:           "empty request body",
			maxSize:        100,
			requestBody:    "",
			expectedStatus: http.StatusOK,
			expectError:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test handler
			testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("success"))
			})

			// Wrap with request size limit middleware
			handler := RequestSizeLimitMiddleware(testHandler, tt.maxSize)

			// Create test request
			req := httptest.NewRequest("POST", "/test", strings.NewReader(tt.requestBody))
			w := httptest.NewRecorder()

			// Execute request
			handler.ServeHTTP(w, req)

			// Check response status
			if w.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, w.Code)
			}

			if tt.expectError {
				// Check error response
				var response map[string]interface{}
				if err := json.Unmarshal(w.Body.Bytes(), &response); err != nil {
					t.Errorf("Failed to parse error response: %v", err)
				}
				if response["success"] != false {
					t.Error("Expected success=false in error response")
				}
				if !strings.Contains(response["error"].(string), "too large") {
					t.Error("Expected 'too large' error message")
				}
			} else {
				// Check successful response
				if w.Body.String() != "success" {
					t.Errorf("Expected 'success', got %s", w.Body.String())
				}
			}
		})
	}
}

func TestMiddlewareChaining(t *testing.T) {
	// Initialize logger for testing
	var buf bytes.Buffer
	config := logging.Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}
	logging.InitGlobalLogger(config)
	logger := logging.GetLogger()
	logger.Logger.SetOutput(&buf)

	// Create test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("chained response"))
	})

	// Chain multiple middleware
	handler := testHandler
	handler = RequestSizeLimitMiddleware(handler, 1000)
	handler = TimeoutMiddleware(handler, 100*time.Millisecond)
	handler = RecoveryMiddleware(handler)
	handler = LoggingMiddleware(handler)

	// Create test request
	req := httptest.NewRequest("POST", "/test", strings.NewReader("test body"))
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}
	if w.Body.String() != "chained response" {
		t.Errorf("Expected 'chained response', got %s", w.Body.String())
	}

	// Check that logging middleware worked
	logOutput := buf.String()
	if !strings.Contains(logOutput, "Request started") {
		t.Error("Expected logging middleware to work in chain")
	}
}

func TestMiddlewareWithContextValues(t *testing.T) {
	// Test that middleware preserves context values
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check if context value is preserved
		if value := r.Context().Value("test_key"); value != "test_value" {
			t.Errorf("Expected context value 'test_value', got %v", value)
		}
		w.WriteHeader(http.StatusOK)
	})

	// Wrap with middleware
	handler := LoggingMiddleware(testHandler)

	// Create request with context value
	req := httptest.NewRequest("GET", "/test", nil)
	ctx := context.WithValue(req.Context(), "test_key", "test_value")
	req = req.WithContext(ctx)
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", w.Code)
	}
}

func TestTimeoutMiddleware_ContextCancellation(t *testing.T) {
	// Test that timeout middleware properly handles context cancellation
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check if context is cancelled
		select {
		case <-r.Context().Done():
			// Context was cancelled, this is expected
			return
		case <-time.After(200 * time.Millisecond):
			// Should not reach here if timeout works
			w.WriteHeader(http.StatusOK)
		}
	})

	// Wrap with short timeout
	handler := TimeoutMiddleware(testHandler, 50*time.Millisecond)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	// Should timeout
	if w.Code != http.StatusRequestTimeout {
		t.Errorf("Expected status %d, got %d", http.StatusRequestTimeout, w.Code)
	}
}

func BenchmarkLoggingMiddleware(b *testing.B) {
	// Initialize logger
	config := logging.Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}
	logging.InitGlobalLogger(config)

	// Create test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	// Wrap with logging middleware
	handler := LoggingMiddleware(testHandler)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)
	}
}

func BenchmarkMiddlewareChain(b *testing.B) {
	// Initialize logger
	config := logging.Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}
	logging.InitGlobalLogger(config)

	// Create test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	// Chain middleware
	handler := testHandler
	handler = RequestSizeLimitMiddleware(handler, 1000000)
	handler = TimeoutMiddleware(handler, 30*time.Second)
	handler = RecoveryMiddleware(handler)
	handler = LoggingMiddleware(handler)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)
	}
}