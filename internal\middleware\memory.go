package middleware

import (
	"context"
	"fmt"
	"net/http"
	"runtime"
	"runtime/debug"
	"sync"
	"time"
)

// MemoryMiddleware provides memory optimization and garbage collection management
type MemoryMiddleware struct {
	maxMemoryMB     int64
	gcInterval      time.Duration
	forceGCThreshold float64
	mu              sync.RWMutex
	lastGC          time.Time
	memoryStats     runtime.MemStats
}

// NewMemoryMiddleware creates a new memory optimization middleware
func NewMemoryMiddleware(maxMemoryMB int64, gcInterval time.Duration, forceGCThreshold float64) *MemoryMiddleware {
	if maxMemoryMB <= 0 {
		maxMemoryMB = 512 // 512MB default
	}
	if gcInterval <= 0 {
		gcInterval = 30 * time.Second // 30 seconds default
	}
	if forceGCThreshold <= 0 || forceGCThreshold > 1 {
		forceGCThreshold = 0.8 // 80% memory usage threshold
	}
	
	mm := &MemoryMiddleware{
		maxMemoryMB:      maxMemoryMB,
		gcInterval:       gcInterval,
		forceGCThreshold: forceGCThreshold,
		lastGC:           time.Now(),
	}
	
	// Start background memory monitoring
	go mm.memoryMonitor()
	
	return mm
}

// memoryOptimizedResponseWriter wraps http.ResponseWriter to track memory usage
type memoryOptimizedResponseWriter struct {
	http.ResponseWriter
	middleware *MemoryMiddleware
	bytesWritten int64
	headerWritten bool
}

// Write tracks bytes written and triggers memory optimization if needed
func (morw *memoryOptimizedResponseWriter) Write(data []byte) (int, error) {
	if !morw.headerWritten {
		morw.WriteHeader(http.StatusOK)
	}
	
	n, err := morw.ResponseWriter.Write(data)
	morw.bytesWritten += int64(n)
	
	// Check if we should trigger garbage collection
	if morw.bytesWritten > 1024*1024 { // After writing 1MB
		morw.middleware.checkMemoryPressure()
	}
	
	return n, err
}

// WriteHeader sets response headers and memory optimization headers
func (morw *memoryOptimizedResponseWriter) WriteHeader(statusCode int) {
	if morw.headerWritten {
		return
	}
	morw.headerWritten = true
	
	// Add memory-related headers for debugging
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	morw.Header().Set("X-Memory-Alloc-MB", formatMemory(m.Alloc))
	morw.Header().Set("X-Memory-Sys-MB", formatMemory(m.Sys))
	
	morw.ResponseWriter.WriteHeader(statusCode)
}

// Handler returns an HTTP handler that applies memory optimization
func (mm *MemoryMiddleware) Handler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Create context with memory tracking
		ctx := context.WithValue(r.Context(), "memory_middleware", mm)
		r = r.WithContext(ctx)
		
		// Wrap response writer with memory tracking
		morw := &memoryOptimizedResponseWriter{
			ResponseWriter: w,
			middleware:     mm,
		}
		
		// Pre-request memory optimization
		mm.preRequestOptimization()
		
		// Execute request
		next.ServeHTTP(morw, r)
		
		// Post-request cleanup
		mm.postRequestCleanup(morw.bytesWritten)
	})
}

// preRequestOptimization performs memory optimization before handling request
func (mm *MemoryMiddleware) preRequestOptimization() {
	// Check if we need to force garbage collection
	if mm.shouldForceGC() {
		mm.forceGarbageCollection()
	}
	
	// Set GC target percentage based on current memory usage
	mm.adjustGCTarget()
}

// postRequestCleanup performs cleanup after handling request
func (mm *MemoryMiddleware) postRequestCleanup(bytesWritten int64) {
	// Force GC for large responses to free memory immediately
	if bytesWritten > 5*1024*1024 { // 5MB threshold
		runtime.GC()
		mm.updateLastGC()
	}
	
	// Return unused memory to OS if significant amount was used
	if bytesWritten > 10*1024*1024 { // 10MB threshold
		debug.FreeOSMemory()
	}
}

// shouldForceGC determines if garbage collection should be forced
func (mm *MemoryMiddleware) shouldForceGC() bool {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	
	// Force GC if interval has passed
	if time.Since(mm.lastGC) > mm.gcInterval {
		return true
	}
	
	// Force GC if memory usage is high
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	maxMemoryBytes := mm.maxMemoryMB * 1024 * 1024
	currentUsage := float64(m.Alloc) / float64(maxMemoryBytes)
	
	return currentUsage > mm.forceGCThreshold
}

// forceGarbageCollection triggers garbage collection and updates stats
func (mm *MemoryMiddleware) forceGarbageCollection() {
	runtime.GC()
	mm.updateLastGC()
}

// updateLastGC updates the last GC timestamp
func (mm *MemoryMiddleware) updateLastGC() {
	mm.mu.Lock()
	mm.lastGC = time.Now()
	mm.mu.Unlock()
}

// adjustGCTarget adjusts the garbage collection target percentage
func (mm *MemoryMiddleware) adjustGCTarget() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	maxMemoryBytes := mm.maxMemoryMB * 1024 * 1024
	currentUsage := float64(m.Alloc) / float64(maxMemoryBytes)
	
	// Adjust GC target based on memory usage
	if currentUsage > 0.7 {
		// High memory usage - more aggressive GC
		debug.SetGCPercent(50)
	} else if currentUsage > 0.5 {
		// Medium memory usage - default GC
		debug.SetGCPercent(100)
	} else {
		// Low memory usage - less aggressive GC
		debug.SetGCPercent(200)
	}
}

// checkMemoryPressure checks current memory pressure and takes action if needed
func (mm *MemoryMiddleware) checkMemoryPressure() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	maxMemoryBytes := mm.maxMemoryMB * 1024 * 1024
	currentUsage := float64(m.Alloc) / float64(maxMemoryBytes)
	
	// If memory usage is very high, force immediate cleanup
	if currentUsage > 0.9 {
		runtime.GC()
		debug.FreeOSMemory()
		mm.updateLastGC()
	}
}

// memoryMonitor runs in background to monitor memory usage
func (mm *MemoryMiddleware) memoryMonitor() {
	ticker := time.NewTicker(mm.gcInterval)
	defer ticker.Stop()
	
	for range ticker.C {
		mm.mu.Lock()
		runtime.ReadMemStats(&mm.memoryStats)
		mm.mu.Unlock()
		
		// Perform periodic cleanup
		if mm.shouldForceGC() {
			mm.forceGarbageCollection()
		}
	}
}

// GetMemoryStats returns current memory statistics
func (mm *MemoryMiddleware) GetMemoryStats() runtime.MemStats {
	mm.mu.RLock()
	defer mm.mu.RUnlock()
	return mm.memoryStats
}

// formatMemory formats memory size in MB
func formatMemory(bytes uint64) string {
	mb := float64(bytes) / 1024 / 1024
	return fmt.Sprintf("%.2f", mb)
}

// HandlerFunc returns a handler function that applies memory optimization
func (mm *MemoryMiddleware) HandlerFunc(next http.HandlerFunc) http.HandlerFunc {
	return mm.Handler(next).ServeHTTP
}

// DefaultMemoryMiddleware creates a memory middleware with default settings
func DefaultMemoryMiddleware() *MemoryMiddleware {
	return NewMemoryMiddleware(512, 30*time.Second, 0.8) // 512MB, 30s interval, 80% threshold
}