package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"qr-background-api/internal/config"
	"qr-background-api/internal/server"
	"qr-background-api/internal/storage"
	"syscall"
	"time"
)

func main() {
	// Load configuration
	cfg, err := config.Load("config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	fmt.Printf("QR Background API Server\n")
	fmt.Printf("Port: %d\n", cfg.Server.Port)
	fmt.Printf("Worker Pool Size: %d\n", cfg.WorkerPool.Size)
	fmt.Printf("Storage Path: %s\n", cfg.Storage.LocalPath)
	fmt.Printf("Performance Optimizations:\n")
	fmt.Printf("  Compression: %v (level %d)\n", cfg.Performance.EnableCompression, cfg.Performance.CompressionLevel)
	fmt.Printf("  Streaming: %v\n", cfg.Performance.EnableStreaming)
	fmt.Printf("  Memory Optimization: %v\n", cfg.Performance.EnableMemoryOpt)

	// Initialize storage components
	storageManager := storage.NewStorageManager(cfg)
	metadataManager := storage.NewFilesystemMetadataManager(cfg.Storage.LocalPath)
	
	// Initialize server with performance optimizations
	srv, err := server.New(cfg, storageManager, metadataManager)
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	// Setup graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start server in a goroutine
	go func() {
		if err := srv.Start(); err != nil {
			log.Printf("Server error: %v", err)
			cancel()
		}
	}()

	log.Println("Server started successfully with performance optimizations")
	log.Printf("Server is running on http://localhost:%d", cfg.Server.Port)
	log.Println("Press Ctrl+C to stop the server")

	// Wait for shutdown signal
	select {
	case <-sigChan:
		log.Println("Shutdown signal received")
	case <-ctx.Done():
		log.Println("Context cancelled")
	}

	// Graceful shutdown with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	log.Println("Shutting down server gracefully...")
	if err := srv.Stop(shutdownCtx); err != nil {
		log.Printf("Error during shutdown: %v", err)
		os.Exit(1)
	}

	log.Println("Server stopped successfully")
}