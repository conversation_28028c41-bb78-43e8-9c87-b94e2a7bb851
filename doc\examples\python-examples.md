# Python Examples

Comprehensive Python examples for integrating with the QR Background API.

## Basic Setup

### API Client Class
```python
import requests
import json
from typing import Dict, Any, Optional, List
from pathlib import Path
import time

class QRBackgroundAPI:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json',
            'User-Agent': 'QRBackgroundAPI-Python/1.0'
        })

    def _request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """Make HTTP request with error handling."""
        url = f"{self.base_url}{endpoint}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            return response
        except requests.exceptions.RequestException as e:
            raise APIError("NETWORK_ERROR", f"Network error: {str(e)}")

    def _handle_response(self, response: requests.Response) -> Dict[str, Any]:
        """Handle API response and errors."""
        try:
            # Handle binary responses (images)
            if response.headers.get('content-type', '').startswith('image/'):
                if not response.ok:
                    raise APIError("HTTP_ERROR", f"HTTP {response.status_code}", response.status_code)
                return {
                    'content': response.content,
                    'headers': dict(response.headers)
                }

            # Handle JSON responses
            data = response.json()
        except ValueError:
            raise APIError("INVALID_RESPONSE", "Invalid JSON response", response.status_code)

        if not response.ok:
            error = data.get('error', {})
            raise APIError(
                error.get('code', 'UNKNOWN_ERROR'),
                error.get('message', 'Unknown error occurred'),
                response.status_code,
                error.get('details')
            )

        if not data.get('success', True):
            error = data.get('error', {})
            raise APIError(
                error.get('code', 'API_ERROR'),
                error.get('message', 'API operation failed'),
                details=error.get('details')
            )

        return data

    def upload_image(self, file_path: str) -> Dict[str, Any]:
        """Upload an image file."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        with open(file_path, 'rb') as f:
            files = {'file': (file_path.name, f, self._get_content_type(file_path))}
            response = self._request('POST', '/upload', files=files)
            return self._handle_response(response)

    def generate_qr(self, qr_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate QR code with background image."""
        response = self._request(
            'POST', 
            '/generate-qr',
            json=qr_data,
            headers={'Content-Type': 'application/json'}
        )
        return self._handle_response(response)

    def list_images(self, page: int = 1, limit: int = 50, folder: Optional[int] = None) -> Dict[str, Any]:
        """List uploaded images with pagination."""
        params = {'page': page, 'limit': limit}
        if folder is not None:
            params['folder'] = folder

        response = self._request('GET', '/management/images', params=params)
        return self._handle_response(response)

    def get_image(self, image_id: str) -> Dict[str, Any]:
        """Get specific image information."""
        response = self._request('GET', f'/management/images/{image_id}')
        return self._handle_response(response)

    def delete_image(self, image_id: str) -> Dict[str, Any]:
        """Delete specific image."""
        response = self._request('DELETE', f'/management/images/{image_id}')
        return self._handle_response(response)

    def cleanup(self, batch_size: Optional[int] = None, max_age_hours: Optional[int] = None) -> Dict[str, Any]:
        """Run cleanup operation."""
        data = {}
        if batch_size is not None:
            data['batch_size'] = batch_size
        if max_age_hours is not None:
            data['max_age_hours'] = max_age_hours

        response = self._request(
            'POST', 
            '/cleanup',
            json=data,
            headers={'Content-Type': 'application/json'}
        )
        return self._handle_response(response)

    def health_check(self) -> Dict[str, Any]:
        """Check API health status."""
        response = self._request('GET', '/health')
        return self._handle_response(response)

    @staticmethod
    def _get_content_type(file_path: Path) -> str:
        """Get content type based on file extension."""
        extension = file_path.suffix.lower()
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif'
        }
        return content_types.get(extension, 'application/octet-stream')


class APIError(Exception):
    """Custom exception for API errors."""
    def __init__(self, code: str, message: str, status: Optional[int] = None, details: Any = None):
        super().__init__(message)
        self.code = code
        self.message = message
        self.status = status
        self.details = details

    def __str__(self):
        return f"[{self.code}] {self.message}"
```

## Upload Examples

### Basic Image Upload
```python
def upload_image_example():
    """Basic image upload example."""
    api = QRBackgroundAPI()
    
    try:
        # Upload image
        result = api.upload_image('background.jpg')
        
        print(f"Upload successful!")
        print(f"Image path: {result['image_path']}")
        print(f"Message: {result['message']}")
        
        return result['image_path']
        
    except APIError as e:
        print(f"Upload failed: [{e.code}] {e.message}")
        if e.details:
            print(f"Details: {e.details}")
        raise
    except FileNotFoundError as e:
        print(f"File error: {e}")
        raise

# Usage
if __name__ == "__main__":
    image_path = upload_image_example()
```

### Batch Upload
```python
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Tuple

def upload_multiple_images(image_paths: List[str], max_workers: int = 5) -> List[Tuple[str, str, Optional[str]]]:
    """Upload multiple images concurrently."""
    api = QRBackgroundAPI()
    results = []
    
    def upload_single(file_path: str) -> Tuple[str, str, Optional[str]]:
        """Upload single image and return result."""
        try:
            result = api.upload_image(file_path)
            return (file_path, result['image_path'], None)
        except Exception as e:
            return (file_path, None, str(e))
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all upload tasks
        future_to_path = {executor.submit(upload_single, path): path for path in image_paths}
        
        # Collect results as they complete
        for future in as_completed(future_to_path):
            file_path = future_to_path[future]
            try:
                result = future.result()
                results.append(result)
                
                if result[2] is None:  # No error
                    print(f"✅ Uploaded: {result[0]} -> {result[1]}")
                else:
                    print(f"❌ Failed: {result[0]} - {result[2]}")
                    
            except Exception as e:
                print(f"❌ Exception for {file_path}: {e}")
                results.append((file_path, None, str(e)))
    
    return results

# Usage
image_files = ['image1.jpg', 'image2.png', 'image3.gif']
results = upload_multiple_images(image_files)

successful_uploads = [r for r in results if r[2] is None]
failed_uploads = [r for r in results if r[2] is not None]

print(f"\nSummary: {len(successful_uploads)} successful, {len(failed_uploads)} failed")
```

### Upload with Progress
```python
import requests
from tqdm import tqdm

class ProgressQRAPI(QRBackgroundAPI):
    """Extended API client with upload progress."""
    
    def upload_image_with_progress(self, file_path: str) -> Dict[str, Any]:
        """Upload image with progress bar."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        file_size = file_path.stat().st_size
        
        with open(file_path, 'rb') as f:
            # Create progress bar
            with tqdm(total=file_size, unit='B', unit_scale=True, desc=f"Uploading {file_path.name}") as pbar:
                
                # Wrapper to track upload progress
                def upload_callback(monitor):
                    pbar.update(monitor.bytes_read - pbar.n)
                
                # Use requests-toolbelt for upload progress (if available)
                try:
                    from requests_toolbelt import MultipartEncoder, MultipartEncoderMonitor
                    
                    encoder = MultipartEncoder(
                        fields={'file': (file_path.name, f, self._get_content_type(file_path))}
                    )
                    monitor = MultipartEncoderMonitor(encoder, upload_callback)
                    
                    response = self._request(
                        'POST', 
                        '/upload',
                        data=monitor,
                        headers={'Content-Type': monitor.content_type}
                    )
                    
                except ImportError:
                    # Fallback without progress tracking
                    print("Install requests-toolbelt for upload progress: pip install requests-toolbelt")
                    files = {'file': (file_path.name, f, self._get_content_type(file_path))}
                    response = self._request('POST', '/upload', files=files)
                    pbar.update(file_size)
                
                return self._handle_response(response)

# Usage
api = ProgressQRAPI()
result = api.upload_image_with_progress('large_image.jpg')
print(f"Upload complete: {result['image_path']}")
```

## QR Generation Examples

### Basic QR Generation
```python
def generate_qr_example():
    """Basic QR code generation example."""
    api = QRBackgroundAPI()
    
    # First upload a background image
    try:
        upload_result = api.upload_image('background.jpg')
        image_path = upload_result['image_path']
        print(f"Background uploaded: {image_path}")
    except APIError as e:
        print(f"Upload failed: {e}")
        return

    # Generate QR code
    qr_config = {
        'data': 'https://example.com',
        'image_path': image_path,
        'x': 100,
        'y': 100,
        'width': 200,
        'height': 200,
        'output_format': 'png'
    }
    
    try:
        result = api.generate_qr(qr_config)
        
        # Save the generated image
        output_file = 'generated_qr.png'
        with open(output_file, 'wb') as f:
            f.write(result['content'])
        
        processing_time = result['headers'].get('x-processing-time-ms', 'unknown')
        print(f"QR code generated successfully!")
        print(f"Processing time: {processing_time}ms")
        print(f"Saved to: {output_file}")
        
    except APIError as e:
        print(f"QR generation failed: [{e.code}] {e.message}")
        if e.details:
            print(f"Details: {e.details}")

# Usage
generate_qr_example()
```

### Advanced QR Generation with Validation
```python
from dataclasses import dataclass
from typing import Union
import re

@dataclass
class QRPosition:
    x: int
    y: int
    width: int
    height: int

class QRGenerator:
    """Advanced QR code generator with validation."""
    
    def __init__(self, api: QRBackgroundAPI):
        self.api = api
        
    def validate_qr_data(self, data: str) -> bool:
        """Validate QR data content."""
        if not data or len(data.strip()) == 0:
            raise ValueError("QR data cannot be empty")
        
        # Check data length (QR codes have limits)
        if len(data) > 4296:  # Approximate limit for QR codes
            raise ValueError("QR data too long (max ~4296 characters)")
        
        return True
    
    def validate_position(self, position: QRPosition, image_path: str) -> bool:
        """Validate QR position against image dimensions."""
        # This would require getting image info first
        # For now, just validate basic constraints
        if position.x < 0 or position.y < 0:
            raise ValueError("QR position cannot be negative")
        
        if position.width < 64 or position.height < 64:
            raise ValueError("QR size must be at least 64x64 pixels")
        
        if position.width > 2048 or position.height > 2048:
            raise ValueError("QR size cannot exceed 2048x2048 pixels")
        
        return True
    
    def generate_url_qr(self, url: str, image_path: str, position: QRPosition, 
                       output_format: str = 'png') -> bytes:
        """Generate QR code for URL with validation."""
        # Validate URL format
        url_pattern = re.compile(
            r'^https?://'  # http:// or https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
            r'localhost|'  # localhost...
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
            r'(?::\d+)?'  # optional port
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not url_pattern.match(url):
            raise ValueError(f"Invalid URL format: {url}")
        
        return self._generate_qr(url, image_path, position, output_format)
    
    def generate_contact_qr(self, name: str, phone: str, email: str, 
                          image_path: str, position: QRPosition,
                          output_format: str = 'png') -> bytes:
        """Generate QR code for contact information (vCard format)."""
        vcard = f"""BEGIN:VCARD
VERSION:3.0
FN:{name}
TEL:{phone}
EMAIL:{email}
END:VCARD"""
        
        return self._generate_qr(vcard, image_path, position, output_format)
    
    def generate_wifi_qr(self, ssid: str, password: str, security: str = 'WPA',
                        image_path: str, position: QRPosition,
                        output_format: str = 'png') -> bytes:
        """Generate QR code for WiFi connection."""
        wifi_string = f"WIFI:T:{security};S:{ssid};P:{password};;"
        return self._generate_qr(wifi_string, image_path, position, output_format)
    
    def _generate_qr(self, data: str, image_path: str, position: QRPosition,
                    output_format: str) -> bytes:
        """Internal QR generation method."""
        self.validate_qr_data(data)
        self.validate_position(position, image_path)
        
        qr_config = {
            'data': data,
            'image_path': image_path,
            'x': position.x,
            'y': position.y,
            'width': position.width,
            'height': position.height,
            'output_format': output_format
        }
        
        result = self.api.generate_qr(qr_config)
        return result['content']

# Usage examples
api = QRBackgroundAPI()
generator = QRGenerator(api)

# Upload background
upload_result = api.upload_image('background.jpg')
image_path = upload_result['image_path']

# Generate different types of QR codes
position = QRPosition(x=100, y=100, width=200, height=200)

# URL QR
url_qr = generator.generate_url_qr('https://example.com', image_path, position)
with open('url_qr.png', 'wb') as f:
    f.write(url_qr)

# Contact QR
contact_qr = generator.generate_contact_qr(
    'John Doe', '+1-555-0123', '<EMAIL>',
    image_path, position
)
with open('contact_qr.png', 'wb') as f:
    f.write(contact_qr)

# WiFi QR
wifi_qr = generator.generate_wifi_qr(
    'MyNetwork', 'MyPassword', 'WPA',
    image_path, position
)
with open('wifi_qr.png', 'wb') as f:
    f.write(wifi_qr)
```

### Batch QR Generation
```python
from typing import List, NamedTuple
import csv

class QRTask(NamedTuple):
    data: str
    position: QRPosition
    output_file: str
    description: str = ""

def generate_batch_qr_codes(api: QRBackgroundAPI, image_path: str, 
                          tasks: List[QRTask], output_format: str = 'png') -> List[dict]:
    """Generate multiple QR codes in batch."""
    results = []
    
    for i, task in enumerate(tasks, 1):
        print(f"Generating QR {i}/{len(tasks)}: {task.description or task.data[:50]}...")
        
        try:
            qr_config = {
                'data': task.data,
                'image_path': image_path,
                'x': task.position.x,
                'y': task.position.y,
                'width': task.position.width,
                'height': task.position.height,
                'output_format': output_format
            }
            
            start_time = time.time()
            result = api.generate_qr(qr_config)
            processing_time = time.time() - start_time
            
            # Save the generated QR code
            with open(task.output_file, 'wb') as f:
                f.write(result['content'])
            
            api_processing_time = result['headers'].get('x-processing-time-ms', 'unknown')
            
            results.append({
                'task': task,
                'success': True,
                'output_file': task.output_file,
                'total_time': processing_time,
                'api_processing_time': api_processing_time,
                'error': None
            })
            
            print(f"  ✅ Saved to {task.output_file} (API: {api_processing_time}ms, Total: {processing_time:.2f}s)")
            
        except Exception as e:
            results.append({
                'task': task,
                'success': False,
                'output_file': None,
                'total_time': None,
                'api_processing_time': None,
                'error': str(e)
            })
            
            print(f"  ❌ Failed: {e}")
    
    return results

# Load QR tasks from CSV
def load_qr_tasks_from_csv(csv_file: str) -> List[QRTask]:
    """Load QR generation tasks from CSV file."""
    tasks = []
    
    with open(csv_file, 'r', newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row in reader:
            position = QRPosition(
                x=int(row['x']),
                y=int(row['y']),
                width=int(row['width']),
                height=int(row['height'])
            )
            
            task = QRTask(
                data=row['data'],
                position=position,
                output_file=row['output_file'],
                description=row.get('description', '')
            )
            
            tasks.append(task)
    
    return tasks

# Example CSV content:
# data,x,y,width,height,output_file,description
# https://example.com/page1,100,100,200,200,qr1.png,Homepage
# https://example.com/page2,300,100,200,200,qr2.png,About Page
# Contact: +1-555-0123,500,100,200,200,qr3.png,Contact Info

# Usage
api = QRBackgroundAPI()

# Upload background
upload_result = api.upload_image('background.jpg')
image_path = upload_result['image_path']

# Load tasks from CSV
tasks = load_qr_tasks_from_csv('qr_tasks.csv')

# Generate all QR codes
results = generate_batch_qr_codes(api, image_path, tasks)

# Print summary
successful = [r for r in results if r['success']]
failed = [r for r in results if not r['success']]

print(f"\n📊 Batch Generation Summary:")
print(f"  ✅ Successful: {len(successful)}")
print(f"  ❌ Failed: {len(failed)}")

if successful:
    avg_time = sum(float(r['api_processing_time'].replace('ms', '')) 
                  for r in successful if r['api_processing_time'] != 'unknown') / len(successful)
    print(f"  ⏱️  Average API processing time: {avg_time:.1f}ms")
```

## Management Examples

### Image Management Class
```python
class ImageManager:
    """Comprehensive image management functionality."""
    
    def __init__(self, api: QRBackgroundAPI):
        self.api = api
    
    def list_all_images(self) -> List[Dict[str, Any]]:
        """Get all images across all pages."""
        all_images = []
        page = 1
        
        while True:
            try:
                response = self.api.list_images(page=page, limit=100)
                images = response['data']['images']
                
                if not images:
                    break
                
                all_images.extend(images)
                
                # Check if there are more pages
                if not response['data']['pagination']['has_next']:
                    break
                
                page += 1
                
            except APIError as e:
                print(f"Error fetching page {page}: {e}")
                break
        
        return all_images
    
    def find_images_by_name(self, name_pattern: str) -> List[Dict[str, Any]]:
        """Find images by original name pattern."""
        all_images = self.list_all_images()
        
        matching_images = []
        for image in all_images:
            if name_pattern.lower() in image['original_name'].lower():
                matching_images.append(image)
        
        return matching_images
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        all_images = self.list_all_images()
        
        total_size = sum(image['file_size'] for image in all_images)
        local_images = [img for img in all_images if img['location'] == 'local']
        cloud_images = [img for img in all_images if img['location'] == 'cloud']
        
        # Group by content type
        by_type = {}
        for image in all_images:
            content_type = image['content_type']
            if content_type not in by_type:
                by_type[content_type] = {'count': 0, 'size': 0}
            by_type[content_type]['count'] += 1
            by_type[content_type]['size'] += image['file_size']
        
        return {
            'total_images': len(all_images),
            'total_size_mb': total_size / (1024 * 1024),
            'local_images': len(local_images),
            'cloud_images': len(cloud_images),
            'by_type': by_type,
            'average_size_mb': (total_size / len(all_images)) / (1024 * 1024) if all_images else 0
        }
    
    def cleanup_old_images(self, days_old: int, dry_run: bool = True) -> List[str]:
        """Delete images older than specified days."""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.now() - timedelta(days=days_old)
        all_images = self.list_all_images()
        
        old_images = []
        for image in all_images:
            created_at = datetime.fromisoformat(image['created_at'].replace('Z', '+00:00'))
            if created_at < cutoff_date:
                old_images.append(image)
        
        print(f"Found {len(old_images)} images older than {days_old} days")
        
        if dry_run:
            print("DRY RUN - No images will be deleted")
            for image in old_images:
                print(f"  Would delete: {image['original_name']} ({image['id']})")
            return [img['id'] for img in old_images]
        
        deleted_ids = []
        for image in old_images:
            try:
                self.api.delete_image(image['id'])
                deleted_ids.append(image['id'])
                print(f"  ✅ Deleted: {image['original_name']}")
            except APIError as e:
                print(f"  ❌ Failed to delete {image['original_name']}: {e}")
        
        return deleted_ids

# Usage examples
api = QRBackgroundAPI()
manager = ImageManager(api)

# Get storage statistics
stats = manager.get_storage_stats()
print("📊 Storage Statistics:")
print(f"  Total images: {stats['total_images']}")
print(f"  Total size: {stats['total_size_mb']:.2f} MB")
print(f"  Local: {stats['local_images']}, Cloud: {stats['cloud_images']}")
print(f"  Average size: {stats['average_size_mb']:.2f} MB")

print("\n📁 By file type:")
for content_type, info in stats['by_type'].items():
    size_mb = info['size'] / (1024 * 1024)
    print(f"  {content_type}: {info['count']} files, {size_mb:.2f} MB")

# Find images by name
matching_images = manager.find_images_by_name('background')
print(f"\n🔍 Found {len(matching_images)} images matching 'background'")

# Cleanup old images (dry run)
old_image_ids = manager.cleanup_old_images(days_old=30, dry_run=True)
```
