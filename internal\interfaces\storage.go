package interfaces

import (
	"image"
	"mime/multipart"
)

// StorageManager handles image storage operations
type StorageManager interface {
	// SaveImage saves an uploaded image file and returns the storage path
	SaveImage(file multipart.File) (string, error)
	
	// LoadImage loads an image from the given path
	LoadImage(path string) (image.Image, error)
	
	// DeleteImage removes an image from storage
	DeleteImage(path string) error
	
	// MoveToCloud moves an image from local storage to cloud storage
	MoveToCloud(path string) error
	
	// FetchFromCloud retrieves an image from cloud storage to local cache
	FetchFromCloud(path string) error
	
	// GenerateUniqueID creates a unique identifier for image storage
	GenerateUniqueID() string
	
	// GetFolderFromID determines which numbered folder (1-1000) to use for an ID
	GetFolderFromID(id string) int
}