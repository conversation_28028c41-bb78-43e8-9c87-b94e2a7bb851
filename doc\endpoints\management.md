# Management Endpoints

Manage uploaded images with listing, retrieval, and deletion capabilities.

## Base Path

All management endpoints are prefixed with `/management/images`

## Endpoints Overview

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/management/images` | List all images with pagination |
| `GET` | `/management/images/{id}` | Get specific image information |
| `DELETE` | `/management/images/{id}` | Delete specific image |

---

## List Images

List all uploaded images with pagination support.

### Endpoint Details
- **URL**: `/management/images`
- **Method**: `GET`
- **Content-Type**: `application/json`

### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `page` | integer | No | Page number (default: 1) |
| `limit` | integer | No | Items per page (default: 50, max: 100) |
| `folder` | integer | No | Filter by specific folder number |

### Request Example
```http
GET /management/images?page=1&limit=20&folder=347
```

### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "images": [
      {
        "id": "abc123def456",
        "image_path": "images/347/abc123def456.jpg",
        "original_name": "background.jpg",
        "file_size": 2048576,
        "content_type": "image/jpeg",
        "created_at": "2025-01-15T10:30:00Z",
        "last_accessed": "2025-01-15T14:20:00Z",
        "location": "local"
      },
      {
        "id": "def456ghi789",
        "image_path": "images/347/def456ghi789.png",
        "original_name": "logo.png",
        "file_size": 1024000,
        "content_type": "image/png",
        "created_at": "2025-01-15T11:15:00Z",
        "last_accessed": "2025-01-15T11:15:00Z",
        "location": "cloud"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "total_pages": 8,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

---

## Get Image Info

Retrieve detailed information about a specific image.

### Endpoint Details
- **URL**: `/management/images/{id}`
- **Method**: `GET`
- **Content-Type**: `application/json`

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | Image ID (filename without extension) |

### Request Example
```http
GET /management/images/abc123def456
```

### Success Response (200 OK)
```json
{
  "success": true,
  "data": {
    "id": "abc123def456",
    "image_path": "images/347/abc123def456.jpg",
    "original_name": "background.jpg",
    "file_size": 2048576,
    "content_type": "image/jpeg",
    "created_at": "2025-01-15T10:30:00Z",
    "last_accessed": "2025-01-15T14:20:00Z",
    "location": "local",
    "metadata": {
      "width": 1920,
      "height": 1080,
      "folder": 347,
      "access_count": 5
    }
  }
}
```

---

## Delete Image

Delete a specific image and its metadata.

### Endpoint Details
- **URL**: `/management/images/{id}`
- **Method**: `DELETE`
- **Content-Type**: `application/json`

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | string | Yes | Image ID (filename without extension) |

### Request Example
```http
DELETE /management/images/abc123def456
```

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Image deleted successfully",
  "data": {
    "id": "abc123def456",
    "image_path": "images/347/abc123def456.jpg",
    "deleted_at": "2025-01-15T15:30:00Z"
  }
}
```

---

## Error Responses

### 404 Not Found - Image Not Found
```json
{
  "success": false,
  "error": {
    "code": "IMAGE_NOT_FOUND",
    "message": "Image not found",
    "details": {
      "image_id": "nonexistent123"
    }
  }
}
```

### 400 Bad Request - Invalid Parameters
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": [
      {
        "field": "limit",
        "message": "Limit must be between 1 and 100",
        "value": 150
      }
    ]
  }
}
```

### 405 Method Not Allowed
```json
{
  "success": false,
  "error": {
    "code": "METHOD_NOT_ALLOWED",
    "message": "Method not allowed",
    "details": "Supported methods: GET, DELETE"
  }
}
```

### 500 Internal Server Error
```json
{
  "success": false,
  "error": {
    "code": "STORAGE_ERROR",
    "message": "Failed to access storage",
    "details": "permission denied"
  }
}
```

## Examples

### cURL Examples

#### List Images
```bash
# List first page with default limit
curl http://localhost:8080/management/images

# List with pagination
curl "http://localhost:8080/management/images?page=2&limit=10"

# Filter by folder
curl "http://localhost:8080/management/images?folder=347"
```

#### Get Image Info
```bash
curl http://localhost:8080/management/images/abc123def456
```

#### Delete Image
```bash
curl -X DELETE http://localhost:8080/management/images/abc123def456
```

### JavaScript Examples

#### List Images
```javascript
// List images with pagination
fetch('http://localhost:8080/management/images?page=1&limit=20')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log(`Found ${data.data.pagination.total} images`);
      data.data.images.forEach(image => {
        console.log(`${image.id}: ${image.original_name} (${image.file_size} bytes)`);
      });
    }
  });
```

#### Get Image Info
```javascript
const imageId = 'abc123def456';
fetch(`http://localhost:8080/management/images/${imageId}`)
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      const image = data.data;
      console.log(`Image: ${image.original_name}`);
      console.log(`Size: ${image.file_size} bytes`);
      console.log(`Location: ${image.location}`);
    } else {
      console.error('Image not found:', data.error);
    }
  });
```

#### Delete Image
```javascript
const imageId = 'abc123def456';
fetch(`http://localhost:8080/management/images/${imageId}`, {
  method: 'DELETE'
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Image deleted successfully');
  } else {
    console.error('Delete failed:', data.error);
  }
});
```

### Python Examples

#### List Images
```python
import requests

# List images with pagination
response = requests.get('http://localhost:8080/management/images', params={
    'page': 1,
    'limit': 20,
    'folder': 347
})

data = response.json()
if data['success']:
    images = data['data']['images']
    pagination = data['data']['pagination']
    
    print(f"Found {pagination['total']} images")
    for image in images:
        print(f"{image['id']}: {image['original_name']} ({image['file_size']} bytes)")
```

#### Get and Delete Image
```python
import requests

image_id = 'abc123def456'

# Get image info
response = requests.get(f'http://localhost:8080/management/images/{image_id}')
if response.status_code == 200:
    image_data = response.json()['data']
    print(f"Image: {image_data['original_name']}")
    
    # Delete image
    delete_response = requests.delete(f'http://localhost:8080/management/images/{image_id}')
    if delete_response.status_code == 200:
        print("Image deleted successfully")
```

## Data Fields

### Image Object Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | string | Unique image identifier |
| `image_path` | string | Full path to image file |
| `original_name` | string | Original filename from upload |
| `file_size` | integer | File size in bytes |
| `content_type` | string | MIME type of the image |
| `created_at` | string | ISO 8601 timestamp of upload |
| `last_accessed` | string | ISO 8601 timestamp of last access |
| `location` | string | Storage location (`local` or `cloud`) |

### Pagination Object Fields

| Field | Type | Description |
|-------|------|-------------|
| `page` | integer | Current page number |
| `limit` | integer | Items per page |
| `total` | integer | Total number of items |
| `total_pages` | integer | Total number of pages |
| `has_next` | boolean | Whether there's a next page |
| `has_prev` | boolean | Whether there's a previous page |
