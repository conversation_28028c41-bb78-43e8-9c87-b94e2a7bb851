package server

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"qr-background-api/internal/compositor"
	"qr-background-api/internal/config"
	"qr-background-api/internal/handlers"
	"qr-background-api/internal/interfaces"
	"qr-background-api/internal/logging"
	"qr-background-api/internal/middleware"
	"qr-background-api/internal/qr"
	"qr-background-api/internal/timeout"
	"qr-background-api/internal/worker"
)

// Server represents the HTTP server with all components
type Server struct {
	config          *config.Config
	server          *http.Server
	workerPool      interfaces.WorkerPool
	handlers        *handlers.Handlers
	middlewareStack *middleware.MiddlewareStack
}

// New creates a new server instance with all components
func New(cfg *config.Config, storageManager interfaces.StorageManager, metadataManager interfaces.MetadataManager) (*Server, error) {
	// Initialize global logger
	loggerConfig := &logging.Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     true,
		StackTrace: false,
	}
	logging.InitGlobalLogger(loggerConfig)
	logger := logging.GetLogger()

	// Initialize global timeout manager
	timeoutConfig := &timeout.Config{
		HTTPRequest:      30 * time.Second,
		FileUpload:       60 * time.Second,
		ImageProcess:     10 * time.Second,
		QRGeneration:     5 * time.Second,
		StorageOp:        20 * time.Second,
		Cleanup:          120 * time.Second,
		HealthCheck:      5 * time.Second,
		GracefulShutdown: 30 * time.Second,
	}
	timeout.InitGlobalManager(timeoutConfig, logger)

	logger.Info("Initializing server components")

	// Create dependencies for worker pool
	bufferPool := worker.NewBufferPool(cfg.WorkerPool.BufferPoolSize, cfg.WorkerPool.BufferPoolSize/2, 1024*1024) // 1MB max buffer
	qrGenerator := qr.NewGenerator()
	compositor := compositor.NewCompositor()

	// Create worker config
	workerConfig := interfaces.WorkerConfig{
		WorkerCount:     cfg.WorkerPool.Size,
		QueueSize:       cfg.WorkerPool.QueueSize,
		JobTimeout:      5 * time.Second,
		ShutdownTimeout: 10 * time.Second,
		BufferPoolSize:  cfg.WorkerPool.BufferPoolSize,
		MaxBufferSize:   1024 * 1024, // 1MB
	}

	// Create worker pool
	workerPool := worker.NewWorkerPool(workerConfig, bufferPool, qrGenerator, compositor, storageManager)

	// Initialize handlers
	handlers := handlers.NewHandlers(storageManager, metadataManager, workerPool, cfg)

	// Create middleware stack
	middlewareStack := createMiddlewareStack(cfg)

	// Create HTTP server
	server := &Server{
		config:          cfg,
		workerPool:      workerPool,
		handlers:        handlers,
		middlewareStack: middlewareStack,
	}

	// Setup HTTP server
	server.setupHTTPServer()

	return server, nil
}

// createMiddlewareStack creates and configures the middleware stack based on config
func createMiddlewareStack(cfg *config.Config) *middleware.MiddlewareStack {
	logger := logging.GetLogger()
	logger.Info("Creating middleware stack with performance optimizations")
	// Create middleware stack with performance optimizations
	if cfg.Performance.EnableCompression || cfg.Performance.EnableStreaming || cfg.Performance.EnableMemoryOpt {
		// Use custom middleware stack with performance features
		compressionLevel := cfg.Performance.CompressionLevel
		if compressionLevel < 1 || compressionLevel > 9 {
			compressionLevel = 6 // Default compression level
		}

		bufferSize := cfg.Performance.StreamBufferSize
		if bufferSize <= 0 {
			bufferSize = 32 * 1024 // 32KB default
		}

		maxMemoryMB := cfg.Performance.MemoryLimit / (1024 * 1024) // Convert bytes to MB
		if maxMemoryMB <= 0 {
			maxMemoryMB = 512 // 512MB default
		}

		gcInterval := time.Duration(cfg.Performance.GCInterval) * time.Second
		if gcInterval <= 0 {
			gcInterval = 30 * time.Second // 30 seconds default
		}

		return middleware.NewCustomMiddlewareStack(
			compressionLevel,
			bufferSize,
			maxMemoryMB,
			gcInterval,
		)
	}

	// Use default middleware stack
	return middleware.NewMiddlewareStack()
}

// setupHTTPServer configures the HTTP server with routes and middleware
func (s *Server) setupHTTPServer() {
	logger := logging.GetLogger()
	logger.Info("Setting up HTTP server")

	mux := http.NewServeMux()

	// Register routes
	s.registerRoutes(mux)

	// Apply middleware stack to the entire mux
	handler := s.middlewareStack.Apply(mux)

	// Add logging middleware
	handler = middleware.LoggingMiddleware(logger)(handler)
	// Add timeout middleware
	handler = middleware.TimeoutMiddleware(30 * time.Second)(handler)
	// Add recovery middleware
	handler = middleware.RecoveryMiddleware(logger)(handler)
	// Add request size limit middleware
	handler = middleware.RequestSizeLimitMiddleware(50 * 1024 * 1024)(handler) // 50MB limit

	// Create HTTP server with timeouts
	s.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", s.config.Server.Port),
		Handler:      handler,
		ReadTimeout:  time.Duration(s.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(s.config.Server.WriteTimeout) * time.Second,
		IdleTimeout:  120 * time.Second,
	}
}

// registerRoutes registers all HTTP routes
func (s *Server) registerRoutes(mux *http.ServeMux) {
	// Health check endpoint
	mux.HandleFunc("/health", s.healthCheckHandler)

	// Upload endpoint
	mux.Handle("/upload", s.handlers.Upload)

	// QR generation endpoint
	mux.Handle("/generate-qr", s.handlers.QRGeneration)

	// Cleanup endpoint
	mux.Handle("/cleanup", s.handlers.Cleanup)

	// Management endpoints
	mux.Handle("/management/", http.StripPrefix("/management", s.handlers.Management))

	log.Println("Routes registered:")
	log.Println("  GET  /health")
	log.Println("  POST /upload")
	log.Println("  POST /generate-qr")
	log.Println("  POST /cleanup")
	log.Println("  *    /management/*")
}

// healthCheckHandler provides a simple health check endpoint
func (s *Server) healthCheckHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(`{"status":"healthy","service":"qr-background-api"}`))
}

// Start starts the HTTP server
func (s *Server) Start() error {
	logger := logging.GetLogger()
	logger.WithFields(logging.Fields{
		"port":             s.config.Server.Port,
		"read_timeout":     s.config.Server.ReadTimeout,
		"write_timeout":    s.config.Server.WriteTimeout,
		"worker_pool_size": s.config.WorkerPool.Size,
		"buffer_pool_size": s.config.WorkerPool.BufferPoolSize,
	}).Info("Starting server with performance optimizations enabled")

	return s.server.ListenAndServe()
}

// Stop gracefully stops the HTTP server
func (s *Server) Stop(ctx context.Context) error {
	logger := logging.GetLogger()
	logger.Info("Stopping server...")

	// Stop worker pool
	if err := s.workerPool.Stop(); err != nil {
		logger.WithError(err).Error("Error stopping worker pool")
	}

	// Shutdown HTTP server
	logger.Info("Shutting down HTTP server")
	return s.server.Shutdown(ctx)
}

// GetServer returns the underlying HTTP server for testing
func (s *Server) GetServer() *http.Server {
	return s.server
}

// GetWorkerPool returns the worker pool for testing
func (s *Server) GetWorkerPool() interfaces.WorkerPool {
	return s.workerPool
}

// GetHandlers returns the handlers for testing
func (s *Server) GetHandlers() *handlers.Handlers {
	return s.handlers
}
