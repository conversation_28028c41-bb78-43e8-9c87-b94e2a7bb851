server:
  port: 8080
  read_timeout: 30
  write_timeout: 30
  enable_compression: true
  compression_level: 6

performance:
  enable_compression: true
  compression_level: 6
  enable_streaming: true
  stream_buffer_size: 32768  # 32KB
  max_stream_memory: 10485760  # 10MB
  enable_memory_optimization: true
  memory_limit: 536870912  # 512MB
  gc_interval_seconds: 30
  force_gc_threshold: 268435456  # 256MB

worker_pool:
  size: 10
  queue_size: 100
  buffer_pool_size: 50

storage:
  local_path: "./storage"
  cloud_bucket: "qr-background-images"
  max_file_size: 10485760  # 10MB

cloud_storage:
  enabled: true
  bucket: "qr-background-images"
  region: "us-east-1"
  access_key: ""  # Leave empty to use environment variables or IAM roles
  secret_key: ""  # Leave empty to use environment variables or IAM roles
  endpoint: ""    # Optional: for custom S3-compatible endpoints

cleanup:
  max_folders: 1000
  batch_size: 100
  max_age_hours: 24