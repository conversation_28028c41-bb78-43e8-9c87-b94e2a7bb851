package timeout

import (
	"context"
	"fmt"
	"time"

	"github.com/sourcegraph/conc"
	"github.com/sourcegraph/conc/pool"
	"qr-background-api/internal/errors"
	"qr-background-api/internal/logging"
)

// Config represents timeout configuration
type Config struct {
	HTTPRequest    time.Duration `yaml:"http_request" json:"http_request"`
	FileUpload     time.Duration `yaml:"file_upload" json:"file_upload"`
	ImageProcess   time.Duration `yaml:"image_process" json:"image_process"`
	QRGeneration   time.Duration `yaml:"qr_generation" json:"qr_generation"`
	StorageOp      time.Duration `yaml:"storage_op" json:"storage_op"`
	Cleanup        time.Duration `yaml:"cleanup" json:"cleanup"`
	HealthCheck    time.Duration `yaml:"health_check" json:"health_check"`
	GracefulShutdown time.Duration `yaml:"graceful_shutdown" json:"graceful_shutdown"`
}

// DefaultConfig returns default timeout configuration
func DefaultConfig() *Config {
	return &Config{
		HTTPRequest:      30 * time.Second,
		FileUpload:       60 * time.Second,
		ImageProcess:     45 * time.Second,
		QRGeneration:     15 * time.Second,
		StorageOp:        30 * time.Second,
		Cleanup:          120 * time.Second,
		HealthCheck:      5 * time.Second,
		GracefulShutdown: 30 * time.Second,
	}
}

// Manager handles timeout operations with structured concurrency
type Manager struct {
	config *Config
	logger *logging.Logger
}

// NewManager creates a new timeout manager
func NewManager(config *Config, logger *logging.Logger) *Manager {
	if config == nil {
		config = DefaultConfig()
	}
	if logger == nil {
		logger = logging.GetLogger()
	}
	return &Manager{
		config: config,
		logger: logger,
	}
}

// WithTimeout executes a function with timeout using conc for structured concurrency
func (m *Manager) WithTimeout(ctx context.Context, timeout time.Duration, operation string, fn func(context.Context) error) error {
	start := time.Now()
	
	// Create context with timeout
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	
	// Use conc.WaitGroup for structured concurrency
	var wg conc.WaitGroup
	var result error
	
	// Execute operation in goroutine
	wg.Go(func() {
		defer func() {
			if r := recover(); r != nil {
				result = errors.ErrInternalServer.WithDetails(map[string]interface{}{
					"panic":     r,
					"operation": operation,
				})
			}
		}()
		
		result = fn(timeoutCtx)
	})
	
	// Wait for completion
	wg.Wait()
	
	// Check if context was cancelled due to timeout
	if timeoutCtx.Err() == context.DeadlineExceeded {
		duration := time.Since(start)
		m.logger.WithFields(logging.Fields{
			"operation": operation,
			"timeout":   timeout.String(),
			"duration":  duration.String(),
		}).Error("Operation timed out")
		
		return errors.ErrTimeout.WithDetails(map[string]interface{}{
			"operation": operation,
			"timeout":   timeout.String(),
			"duration":  duration.String(),
		})
	}
	
	// Log performance metrics
	duration := time.Since(start)
	m.logger.LogPerformance(operation, duration, logging.Fields{
		"timeout_configured": timeout.String(),
	})
	
	return result
}

// WithHTTPTimeout executes HTTP-related operations with appropriate timeout
func (m *Manager) WithHTTPTimeout(ctx context.Context, operation string, fn func(context.Context) error) error {
	return m.WithTimeout(ctx, m.config.HTTPRequest, fmt.Sprintf("http_%s", operation), fn)
}

// WithFileUploadTimeout executes file upload operations with timeout
func (m *Manager) WithFileUploadTimeout(ctx context.Context, operation string, fn func(context.Context) error) error {
	return m.WithTimeout(ctx, m.config.FileUpload, fmt.Sprintf("upload_%s", operation), fn)
}

// WithImageProcessTimeout executes image processing operations with timeout
func (m *Manager) WithImageProcessTimeout(ctx context.Context, operation string, fn func(context.Context) error) error {
	return m.WithTimeout(ctx, m.config.ImageProcess, fmt.Sprintf("image_%s", operation), fn)
}

// WithQRGenerationTimeout executes QR generation operations with timeout
func (m *Manager) WithQRGenerationTimeout(ctx context.Context, operation string, fn func(context.Context) error) error {
	return m.WithTimeout(ctx, m.config.QRGeneration, fmt.Sprintf("qr_%s", operation), fn)
}

// WithStorageTimeout executes storage operations with timeout
func (m *Manager) WithStorageTimeout(ctx context.Context, operation string, fn func(context.Context) error) error {
	return m.WithTimeout(ctx, m.config.StorageOp, fmt.Sprintf("storage_%s", operation), fn)
}

// WithCleanupTimeout executes cleanup operations with timeout
func (m *Manager) WithCleanupTimeout(ctx context.Context, operation string, fn func(context.Context) error) error {
	return m.WithTimeout(ctx, m.config.Cleanup, fmt.Sprintf("cleanup_%s", operation), fn)
}

// WithHealthCheckTimeout executes health check operations with timeout
func (m *Manager) WithHealthCheckTimeout(ctx context.Context, operation string, fn func(context.Context) error) error {
	return m.WithTimeout(ctx, m.config.HealthCheck, fmt.Sprintf("health_%s", operation), fn)
}

// ExecuteWithRetry executes an operation with retry logic and timeout
func (m *Manager) ExecuteWithRetry(ctx context.Context, timeout time.Duration, operation string, maxRetries int, fn func(context.Context) error) error {
	var lastErr error
	
	for attempt := 1; attempt <= maxRetries; attempt++ {
		err := m.WithTimeout(ctx, timeout, fmt.Sprintf("%s_attempt_%d", operation, attempt), fn)
		
		if err == nil {
			// Success
			if attempt > 1 {
				m.logger.WithFields(logging.Fields{
					"operation": operation,
					"attempt":   attempt,
					"max_retries": maxRetries,
				}).Info("Operation succeeded after retry")
			}
			return nil
		}
		
		lastErr = err
		
		// Check if error is retryable
		if !errors.IsRetryableError(err) {
			m.logger.WithFields(logging.Fields{
				"operation": operation,
				"attempt":   attempt,
				"error":     err.Error(),
			}).Warn("Non-retryable error, stopping retries")
			return err
		}
		
		// Don't retry on last attempt
		if attempt == maxRetries {
			break
		}
		
		// Calculate backoff delay
		backoffDelay := time.Duration(attempt) * time.Second
		if backoffDelay > 10*time.Second {
			backoffDelay = 10 * time.Second
		}
		
		m.logger.WithFields(logging.Fields{
			"operation": operation,
			"attempt":   attempt,
			"max_retries": maxRetries,
			"error":     err.Error(),
			"backoff":   backoffDelay.String(),
		}).Warn("Operation failed, retrying")
		
		// Wait before retry
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(backoffDelay):
			// Continue to next attempt
		}
	}
	
	m.logger.WithFields(logging.Fields{
		"operation": operation,
		"max_retries": maxRetries,
		"final_error": lastErr.Error(),
	}).Error("Operation failed after all retries")
	
	return lastErr
}

// ExecuteConcurrent executes multiple operations concurrently with timeout
func (m *Manager) ExecuteConcurrent(ctx context.Context, timeout time.Duration, operations map[string]func(context.Context) error) map[string]error {
	// Create context with timeout
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	
	// Use conc pool for concurrent execution
	p := pool.New()
	p.WithMaxGoroutines(len(operations))
	p.WithContext(timeoutCtx)
	
	results := make(map[string]error)
	
	// Submit all operations
	for name, operation := range operations {
		name := name // Capture loop variable
		operation := operation
		
		p.Go(func() {
			start := time.Now()
			
			defer func() {
				if r := recover(); r != nil {
					m.logger.WithFields(logging.Fields{
						"operation": name,
						"panic":     r,
					}).Error("Panic in concurrent operation")
					results[name] = fmt.Errorf("panic: %v", r)
				}
			}()
			
			err := operation(timeoutCtx)
			duration := time.Since(start)
			
			m.logger.LogPerformance(fmt.Sprintf("concurrent_%s", name), duration, logging.Fields{
				"timeout_configured": timeout.String(),
			})
			
			results[name] = err
		})
	}
	
	// Wait for all operations to complete
	p.Wait()
	
	// Check for timeout
	if timeoutCtx.Err() == context.DeadlineExceeded {
		m.logger.WithFields(logging.Fields{
			"operations": len(operations),
			"timeout":    timeout.String(),
		}).Error("Concurrent operations timed out")
		
		// Mark incomplete operations as timed out
		for name := range operations {
			if _, exists := results[name]; !exists {
				results[name] = errors.ErrTimeout.WithDetails(map[string]interface{}{
					"operation": name,
					"timeout":   timeout.String(),
				})
			}
		}
	}
	
	return results
}

// GetConfig returns the timeout configuration
func (m *Manager) GetConfig() *Config {
	return m.config
}

// UpdateConfig updates the timeout configuration
func (m *Manager) UpdateConfig(config *Config) {
	m.config = config
	m.logger.WithFields(logging.Fields{
		"http_request":      config.HTTPRequest.String(),
		"file_upload":       config.FileUpload.String(),
		"image_process":     config.ImageProcess.String(),
		"qr_generation":     config.QRGeneration.String(),
		"storage_op":        config.StorageOp.String(),
		"cleanup":           config.Cleanup.String(),
		"health_check":      config.HealthCheck.String(),
		"graceful_shutdown": config.GracefulShutdown.String(),
	}).Info("Timeout configuration updated")
}

// Global timeout manager instance
var defaultManager *Manager

// InitGlobalManager initializes the global timeout manager
func InitGlobalManager(config *Config, logger *logging.Logger) {
	defaultManager = NewManager(config, logger)
}

// GetManager returns the global timeout manager
func GetManager() *Manager {
	if defaultManager == nil {
		defaultManager = NewManager(DefaultConfig(), logging.GetLogger())
	}
	return defaultManager
}