# Design Document

## Overview

The QR Background API is a high-performance Go service that provides endpoints for uploading background images and generating QR codes positioned on those backgrounds. The system is designed for maximum performance with sub-80ms processing times, utilizing worker pools, parallel processing, and hybrid storage management.

## Architecture

### Core Components

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   HTTP Router   │────│  Request Handler │────│  Worker Pool    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                │                        │
                       ┌────────▼────────┐    ┌─────────▼─────────┐
                       │ Storage Manager │    │ QR Generator      │
                       └─────────────────┘    └───────────────────┘
                                │                        │
                       ┌────────▼────────┐    ┌─────────▼─────────┐
                       │ Database Client │    │ Image Compositor  │
                       └─────────────────┘    └───────────────────┘
```

### Request Flow

1. **Upload Flow**: HTTP Request → Validation → Generate Unique ID → File Storage → Metadata File → Response
2. **QR Generation Flow**: HTTP Request → Worker Pool → Parallel (QR Gen + Image Load) → Composition → Response
3. **Cleanup Flow**: HTTP Request → Folder Selection → Metadata Scan → Batch Processing → Storage Operations → Response

## Components and Interfaces

### 1. HTTP Server Layer

**Router Interface**
```go
type Router interface {
    POST("/upload", uploadHandler)
    POST("/generate-qr", generateQRHandler)
    POST("/cleanup", cleanupHandler)
    GET("/images", listImagesHandler)
    DELETE("/images/{id}", deleteImageHandler)
}
```

**Request/Response Models**
```go
type UploadRequest struct {
    File multipart.File `form:"file"`
}

type UploadResponse struct {
    ImagePath string `json:"image_path"`
    Success   bool   `json:"success"`
}

type QRRequest struct {
    Data         string `json:"data"`
    ImagePath    string `json:"image_path"`
    X            int    `json:"x"`
    Y            int    `json:"y"`
    Width        int    `json:"width"`
    Height       int    `json:"height"`
    OutputFormat string `json:"output_format"`
}

type CleanupRequest struct {
    BatchSize int `json:"batch_size"`
    MaxAge    int `json:"max_age_hours"`
}
```

### 2. Worker Pool System

**Worker Pool Interface**
```go
type WorkerPool interface {
    Submit(job QRJob) <-chan QRResult
    Start(workerCount int)
    Stop()
}

type QRJob struct {
    Request    QRRequest
    ResultChan chan<- QRResult
}

type QRResult struct {
    ImageData []byte
    Error     error
}
```

**Memory Buffer Pool**
```go
type BufferPool interface {
    GetQRBuffer() *bytes.Buffer
    GetImageBuffer() *bytes.Buffer
    ReturnQRBuffer(*bytes.Buffer)
    ReturnImageBuffer(*bytes.Buffer)
}
```

### 3. Storage Management

**Storage Interface**
```go
type StorageManager interface {
    SaveImage(file multipart.File) (string, error)
    LoadImage(path string) (image.Image, error)
    DeleteImage(path string) error
    MoveToCloud(path string) error
    FetchFromCloud(path string) error
    GenerateUniqueID() string
    GetFolderFromID(id string) int
}
```

**Directory Structure**
```
/storage/
├── images/
│   ├── 1/
│   │   ├── abc123def456.jpg
│   │   └── xyz789uvw012.png
│   ├── 2/
│   ├── 3/
│   └── ... (up to 1000)
└── temp/
    └── processing/
```

### 4. Database Layer

**Filesystem Metadata Interface**
```go
type MetadataManager interface {
    SaveImageMetadata(imagePath string, metadata ImageMetadata) error
    UpdateLastAccess(imagePath string) error
    GetImagesForCleanup(folderNum int, maxAge time.Duration, limit int) ([]ImageMetadata, error)
    DeleteImageMetadata(imagePath string) error
    GetLastProcessedFolder() (int, error)
    SetLastProcessedFolder(folderNum int) error
}

type ImageMetadata struct {
    ImagePath    string    `json:"image_path"`
    OriginalName string    `json:"original_name"`
    FileSize     int64     `json:"file_size"`
    ContentType  string    `json:"content_type"`
    CreatedAt    time.Time `json:"created_at"`
    LastAccessed time.Time `json:"last_accessed"`
    Location     string    `json:"location"` // "local" or "cloud"
}
```

### 5. QR Generation Engine

**QR Generator Interface**
```go
type QRGenerator interface {
    Generate(data string, width, height int) (image.Image, error)
    GenerateToBuffer(data string, width, height int, buf *bytes.Buffer) error
}
```

**Image Compositor Interface**
```go
type ImageCompositor interface {
    Composite(background image.Image, qr image.Image, x, y int) (image.Image, error)
    CompositeToBuffer(background image.Image, qr image.Image, x, y int, buf *bytes.Buffer, format string) error
}
```

## Data Models

### Filesystem Metadata Structure

**Metadata Files:**
- Each image has a corresponding `.meta` file: `abc123def456.jpg.meta`
- Cleanup state stored in: `/storage/cleanup_state.json`

**Metadata File Format (JSON):**
```json
{
    "image_path": "images/347/abc123def456.jpg",
    "original_name": "background.jpg",
    "file_size": 2048576,
    "content_type": "image/jpeg",
    "created_at": "2025-01-15T10:30:00Z",
    "last_accessed": "2025-01-15T14:20:00Z",
    "location": "local"
}
```

**Cleanup State File:**
```json
{
    "last_processed_folder": 347,
    "last_cleanup_run": "2025-01-15T12:00:00Z"
}
```

### Configuration Model

```go
type Config struct {
    Server struct {
        Port         int    `yaml:"port"`
        ReadTimeout  int    `yaml:"read_timeout"`
        WriteTimeout int    `yaml:"write_timeout"`
    } `yaml:"server"`
    
    WorkerPool struct {
        Size           int `yaml:"size"`
        QueueSize      int `yaml:"queue_size"`
        BufferPoolSize int `yaml:"buffer_pool_size"`
    } `yaml:"worker_pool"`
    
    Storage struct {
        LocalPath   string `yaml:"local_path"`
        CloudBucket string `yaml:"cloud_bucket"`
        MaxFileSize int64  `yaml:"max_file_size"`
    } `yaml:"storage"`
    
    Cleanup struct {
        MaxFolders     int `yaml:"max_folders"`     // 1000
        BatchSize      int `yaml:"batch_size"`      // images per folder
        MaxAgeHours    int `yaml:"max_age_hours"`   // 24
    } `yaml:"cleanup"`
}
```

## Error Handling

### Error Types

```go
type APIError struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}

var (
    ErrInvalidImageFormat = APIError{400, "Invalid image format", ""}
    ErrFileTooLarge      = APIError{400, "File too large", ""}
    ErrImageNotFound     = APIError{404, "Image not found", ""}
    ErrQRGenerationFailed = APIError{500, "QR generation failed", ""}
    ErrImageProcessingFailed = APIError{500, "Image processing failed", ""}
)
```

### Error Handling Strategy

1. **Input Validation**: Validate all inputs at handler level
2. **Graceful Degradation**: Continue processing other requests if one fails
3. **Resource Cleanup**: Always cleanup resources in defer statements
4. **Logging**: Log all errors with context for debugging
5. **Circuit Breaker**: Implement circuit breaker for external services (cloud storage)

## Testing Strategy

### Unit Testing

1. **Handler Tests**: Test all HTTP endpoints with various inputs
2. **Worker Pool Tests**: Test concurrent processing and resource management
3. **Storage Tests**: Test file operations and error conditions
4. **QR Generation Tests**: Test QR generation with different parameters
5. **Database Tests**: Test all database operations with mock data

### Integration Testing

1. **End-to-End Flow**: Test complete upload → generate QR → cleanup flow
2. **Performance Tests**: Verify sub-80ms processing time requirements
3. **Concurrency Tests**: Test system under high concurrent load
4. **Storage Integration**: Test local storage and cloud storage operations
5. **Filesystem Integration**: Test filesystem metadata operations under load

### Performance Testing

1. **Load Testing**: Test with high request volume
2. **Stress Testing**: Test system limits and failure modes
3. **Memory Testing**: Verify memory usage and leak detection
4. **Latency Testing**: Verify 80ms processing time target
5. **Resource Testing**: Monitor CPU, memory, and I/O usage

### Test Data Management

1. **Test Images**: Prepare various image formats and sizes
2. **Mock Services**: Mock cloud storage and database for unit tests
3. **Performance Benchmarks**: Establish baseline performance metrics
4. **Test Cleanup**: Automated cleanup of test data and resources

## Performance Optimizations

### 1. Parallel Processing
- QR generation and background image loading run concurrently
- Use goroutines with proper synchronization
- Implement timeout mechanisms for all operations

### 2. Memory Management
- Pre-allocated buffer pools for image processing
- Immediate memory release after processing
- Garbage collection optimization

### 3. I/O Optimization
- Directory sharding to avoid filesystem limits
- Streaming for large file operations
- Async database updates for non-critical operations

### 4. Worker Pool Benefits
- Controlled resource usage
- Memory buffer reuse
- Better handling of concurrent requests
- Queue management during high load

### 5. Caching Strategy
- No caching for QR codes (always different)
- LRU cache for frequently accessed background images
- Filesystem metadata caching
- HTTP response compression