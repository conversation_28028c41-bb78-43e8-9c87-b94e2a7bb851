package interfaces

import (
	"context"
	"io"
)

// CloudStorageManager handles cloud storage operations
type CloudStorageManager interface {
	// UploadFile uploads a file to cloud storage
	UploadFile(ctx context.Context, key string, reader io.Reader, contentType string) error
	
	// DownloadFile downloads a file from cloud storage
	DownloadFile(ctx context.Context, key string, writer io.Writer) error
	
	// DeleteFile deletes a file from cloud storage
	DeleteFile(ctx context.Context, key string) error
	
	// FileExists checks if a file exists in cloud storage
	FileExists(ctx context.Context, key string) (bool, error)
	
	// GetFileSize returns the size of a file in cloud storage
	GetFileSize(ctx context.Context, key string) (int64, error)
}

// CloudStorageConfig holds configuration for cloud storage
type CloudStorageConfig struct {
	Bucket    string
	Region    string
	AccessKey string
	SecretKey string
	Endpoint  string // Optional for custom S3-compatible endpoints
}