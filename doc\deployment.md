# Deployment Guide

Complete guide for deploying the QR Background API in various environments.

## Prerequisites

### System Requirements
- **OS**: Linux (Ubuntu 20.04+, CentOS 8+, RHEL 8+)
- **CPU**: 2+ cores (4+ recommended for production)
- **RAM**: 1GB minimum (4GB+ recommended for production)
- **Storage**: 10GB+ available space
- **Network**: Stable internet connection for dependencies

### Software Dependencies
- **Go**: 1.19 or higher
- **Git**: For source code management
- **systemd**: For service management (optional)

## Build and Installation

### 1. Clone Repository
```bash
git clone https://github.com/your-org/qr-background-api.git
cd qr-background-api
```

### 2. Build Application
```bash
# Install dependencies
go mod tidy

# Build for production
go build -o qr-background-api cmd/server/main.go

# Or build with optimizations
go build -ldflags="-s -w" -o qr-background-api cmd/server/main.go
```

### 3. Create Directory Structure
```bash
# Create application directory
sudo mkdir -p /opt/qr-background-api
sudo mkdir -p /opt/qr-background-api/storage
sudo mkdir -p /opt/qr-background-api/logs
sudo mkdir -p /etc/qr-background-api

# Copy binary and config
sudo cp qr-background-api /opt/qr-background-api/
sudo cp config.yaml /etc/qr-background-api/

# Set permissions
sudo chown -R qr-api:qr-api /opt/qr-background-api
sudo chmod +x /opt/qr-background-api/qr-background-api
```

### 4. Create Service User
```bash
# Create dedicated user for the service
sudo useradd --system --shell /bin/false --home /opt/qr-background-api qr-api
sudo usermod -a -G qr-api qr-api
```

## Configuration

### Production Configuration
```yaml
# /etc/qr-background-api/config.yaml
server:
  port: 8080
  read_timeout: 30
  write_timeout: 30
  enable_compression: true
  compression_level: 6

performance:
  enable_compression: true
  compression_level: 6
  enable_streaming: true
  stream_buffer_size: 32768
  max_stream_memory: 10485760
  enable_memory_optimization: true
  memory_limit: 1073741824      # 1GB
  gc_interval_seconds: 30
  force_gc_threshold: 536870912 # 512MB

worker_pool:
  size: 10                      # Adjust based on CPU cores
  queue_size: 100
  buffer_pool_size: 50

storage:
  local_path: "/opt/qr-background-api/storage"
  max_file_size: 10485760       # 10MB

cloud_storage:
  enabled: false                # Enable if using cloud storage
  bucket: "qr-background-images"
  region: "us-east-1"
  access_key: ""               # Set via environment variables
  secret_key: ""               # Set via environment variables

cleanup:
  max_folders: 1000
  batch_size: 100
  max_age_hours: 24

logging:
  level: "info"
  format: "json"
  output: "file"
  file_path: "/opt/qr-background-api/logs/api.log"
  max_file_size: 100
  max_backups: 5
  max_age_days: 30

timeouts:
  http_request: 30
  file_upload: 60
  image_process: 10
  qr_generation: 5
  storage_op: 20
  cleanup: 120
  health_check: 5
  graceful_shutdown: 30
```

### Environment Variables
```bash
# /etc/environment or systemd service file
QR_API_SERVER_PORT=8080
QR_API_STORAGE_LOCAL_PATH="/opt/qr-background-api/storage"
QR_API_LOGGING_FILE_PATH="/opt/qr-background-api/logs/api.log"

# Cloud storage (if enabled)
QR_API_CLOUD_STORAGE_ACCESS_KEY="your-access-key"
QR_API_CLOUD_STORAGE_SECRET_KEY="your-secret-key"
```

## Service Management

### systemd Service
Create `/etc/systemd/system/qr-background-api.service`:

```ini
[Unit]
Description=QR Background API Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=qr-api
Group=qr-api
WorkingDirectory=/opt/qr-background-api
ExecStart=/opt/qr-background-api/qr-background-api -config /etc/qr-background-api/config.yaml
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=qr-background-api

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/qr-background-api/storage /opt/qr-background-api/logs

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Environment
Environment=QR_API_SERVER_PORT=8080
EnvironmentFile=-/etc/qr-background-api/environment

[Install]
WantedBy=multi-user.target
```

### Service Management Commands
```bash
# Enable and start service
sudo systemctl enable qr-background-api
sudo systemctl start qr-background-api

# Check status
sudo systemctl status qr-background-api

# View logs
sudo journalctl -u qr-background-api -f

# Restart service
sudo systemctl restart qr-background-api

# Stop service
sudo systemctl stop qr-background-api
```

## Reverse Proxy Setup

### Nginx Configuration
Create `/etc/nginx/sites-available/qr-background-api`:

```nginx
upstream qr_backend {
    server 127.0.0.1:8080;
    keepalive 32;
}

server {
    listen 80;
    server_name your-api-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-api-domain.com;
    
    # SSL configuration
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # Client upload limits
    client_max_body_size 50M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Proxy settings
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    
    # Timeouts
    proxy_connect_timeout 30s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    location / {
        proxy_pass http://qr_backend;
    }
    
    # Health check endpoint (no auth required)
    location /health {
        proxy_pass http://qr_backend;
        access_log off;
    }
    
    # Optional: Rate limiting
    location /upload {
        limit_req zone=upload_limit burst=5 nodelay;
        proxy_pass http://qr_backend;
    }
    
    location /generate-qr {
        limit_req zone=qr_limit burst=10 nodelay;
        proxy_pass http://qr_backend;
    }
}

# Rate limiting zones (add to http block in nginx.conf)
http {
    limit_req_zone $binary_remote_addr zone=upload_limit:10m rate=10r/m;
    limit_req_zone $binary_remote_addr zone=qr_limit:10m rate=60r/m;
}
```

### Enable Nginx Site
```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/qr-background-api /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

## Docker Deployment

### Dockerfile
```dockerfile
# Build stage
FROM golang:1.19-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o qr-background-api cmd/server/main.go

# Runtime stage
FROM alpine:3.18

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates tzdata

# Create non-root user
RUN addgroup -g 1001 -S qr-api && \
    adduser -u 1001 -S qr-api -G qr-api

# Create directories
RUN mkdir -p /app/storage /app/logs /app/config
RUN chown -R qr-api:qr-api /app

WORKDIR /app

# Copy binary and config
COPY --from=builder /app/qr-background-api .
COPY --chown=qr-api:qr-api config.yaml ./config/

USER qr-api

EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

CMD ["./qr-background-api", "-config", "./config/config.yaml"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  qr-background-api:
    build: .
    ports:
      - "8080:8080"
    volumes:
      - ./storage:/app/storage
      - ./logs:/app/logs
      - ./config.yaml:/app/config/config.yaml:ro
    environment:
      - QR_API_SERVER_PORT=8080
      - QR_API_LOGGING_LEVEL=info
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl:ro
    depends_on:
      - qr-background-api
    restart: unless-stopped
```

### Docker Commands
```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f qr-background-api

# Scale service
docker-compose up -d --scale qr-background-api=3

# Update service
docker-compose pull
docker-compose up -d
```

## Kubernetes Deployment

### Deployment Manifest
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: qr-background-api
  labels:
    app: qr-background-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: qr-background-api
  template:
    metadata:
      labels:
        app: qr-background-api
    spec:
      containers:
      - name: qr-background-api
        image: your-registry/qr-background-api:latest
        ports:
        - containerPort: 8080
        env:
        - name: QR_API_SERVER_PORT
          value: "8080"
        - name: QR_API_STORAGE_LOCAL_PATH
          value: "/app/storage"
        volumeMounts:
        - name: storage
          mountPath: /app/storage
        - name: config
          mountPath: /app/config
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: storage
        persistentVolumeClaim:
          claimName: qr-api-storage
      - name: config
        configMap:
          name: qr-api-config

---
apiVersion: v1
kind: Service
metadata:
  name: qr-background-api-service
spec:
  selector:
    app: qr-background-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: qr-api-storage
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: qr-api-config
data:
  config.yaml: |
    server:
      port: 8080
    worker_pool:
      size: 10
    storage:
      local_path: "/app/storage"
    # ... rest of config
```

### Ingress Configuration
```yaml
# k8s-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: qr-background-api-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
spec:
  tls:
  - hosts:
    - api.yourdomain.com
    secretName: qr-api-tls
  rules:
  - host: api.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: qr-background-api-service
            port:
              number: 80
```

## Monitoring and Logging

### Log Aggregation
```bash
# Using journald
sudo journalctl -u qr-background-api -f --output=json

# Using ELK Stack
# Configure Filebeat to ship logs to Elasticsearch
```

### Monitoring Setup
```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'qr-background-api'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'  # If metrics endpoint is implemented
    scrape_interval: 15s
```

## Security Considerations

### Firewall Configuration
```bash
# UFW (Ubuntu)
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 8080/tcp   # Block direct access to API
sudo ufw enable
```

### SSL/TLS Setup
```bash
# Using Let's Encrypt with Certbot
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-api-domain.com
```

### Security Headers
Already included in the Nginx configuration above.

## Backup and Recovery

### Backup Strategy
```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/qr-api"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p "$BACKUP_DIR/$DATE"

# Backup storage
tar -czf "$BACKUP_DIR/$DATE/storage.tar.gz" /opt/qr-background-api/storage/

# Backup configuration
cp /etc/qr-background-api/config.yaml "$BACKUP_DIR/$DATE/"

# Backup logs (last 7 days)
find /opt/qr-background-api/logs/ -name "*.log" -mtime -7 -exec cp {} "$BACKUP_DIR/$DATE/" \;

# Clean old backups (keep 30 days)
find "$BACKUP_DIR" -type d -mtime +30 -exec rm -rf {} \;
```

### Recovery Process
```bash
#!/bin/bash
# restore.sh
BACKUP_DATE=$1

if [ -z "$BACKUP_DATE" ]; then
    echo "Usage: $0 <backup_date>"
    exit 1
fi

# Stop service
sudo systemctl stop qr-background-api

# Restore storage
sudo tar -xzf "/backup/qr-api/$BACKUP_DATE/storage.tar.gz" -C /

# Restore configuration
sudo cp "/backup/qr-api/$BACKUP_DATE/config.yaml" /etc/qr-background-api/

# Set permissions
sudo chown -R qr-api:qr-api /opt/qr-background-api/storage

# Start service
sudo systemctl start qr-background-api
```

## Troubleshooting

### Common Issues
1. **Service won't start**: Check logs with `journalctl -u qr-background-api`
2. **Permission denied**: Verify file ownership and permissions
3. **Port already in use**: Check for conflicting services
4. **Out of disk space**: Monitor storage usage and cleanup old files
5. **Memory issues**: Adjust memory limits in configuration

### Health Checks
```bash
# Check service status
curl http://localhost:8080/health

# Check system resources
htop
df -h
free -h

# Check logs
tail -f /opt/qr-background-api/logs/api.log
```
