# API Overview

## Base Information

- **Base URL**: `http://localhost:8080`
- **Protocol**: HTTP/1.1
- **Content-Type**: `application/json` (except file uploads)
- **Response Format**: JSON
- **Default Port**: 8080

## Architecture

The QR Background API is built with a high-performance architecture designed for concurrent processing:

### Core Components

1. **HTTP Server Layer**
   - Built with <PERSON>'s native `net/http`
   - Middleware stack for compression, logging, and memory optimization
   - Request timeout management
   - Graceful shutdown support

2. **Worker Pool System**
   - Concurrent QR generation processing
   - Configurable worker count (default: 10)
   - Queue-based job distribution
   - Sub-80ms processing requirement

3. **Storage Management**
   - Local filesystem storage
   - Optional cloud storage integration (S3-compatible)
   - Automatic file organization by folders
   - Metadata tracking for cleanup operations

4. **Image Processing**
   - QR code generation with custom positioning
   - Image composition and overlay
   - Multiple output formats (PNG, JPEG, GIF)
   - Memory-optimized streaming

## Request/Response Flow

### 1. Upload Flow
```
HTTP Request → Validation → Generate Unique ID → File Storage → Metadata File → Response
```

### 2. QR Generation Flow
```
HTTP Request → Worker Pool → Parallel (QR Gen + Image Load) → Composition → Response
```

### 3. Cleanup Flow
```
HTTP Request → Folder Selection → Metadata Scan → Batch Processing → Storage Operations → Response
```

## Available Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/health` | Health check endpoint |
| `POST` | `/upload` | Upload background image |
| `POST` | `/generate-qr` | Generate QR code with background |
| `POST` | `/cleanup` | Clean up old images |
| `GET` | `/management/images` | List all images |
| `GET` | `/management/images/{id}` | Get specific image info |
| `DELETE` | `/management/images/{id}` | Delete specific image |

## Common Headers

### Request Headers
```http
Content-Type: application/json
Accept: application/json
User-Agent: YourApp/1.0
```

### Response Headers
```http
Content-Type: application/json
X-Request-ID: uuid-string
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
```

## Rate Limiting

The API does not implement artificial rate limiting but relies on:
- Worker pool capacity for QR generation
- Memory management for large file uploads
- Timeout mechanisms for request processing

## Timeouts

| Operation | Timeout |
|-----------|---------|
| HTTP Request | 30 seconds |
| File Upload | 60 seconds |
| Image Processing | 10 seconds |
| QR Generation | 5 seconds |
| Storage Operations | 20 seconds |
| Cleanup Operations | 120 seconds |
| Health Check | 5 seconds |

## Performance Characteristics

- **QR Generation**: < 80ms processing time requirement
- **Concurrent Processing**: Up to 10 parallel QR generation jobs
- **Memory Usage**: Optimized with streaming and garbage collection
- **File Size Limits**: 10MB per upload (configurable)
- **Supported Formats**: JPEG, PNG, GIF

## Error Handling

All endpoints return structured error responses with:
- HTTP status codes
- Error codes for programmatic handling
- Human-readable messages
- Optional detailed information

Example error response:
```json
{
  "success": false,
  "error": {
    "code": "INVALID_IMAGE_FORMAT",
    "message": "Invalid image format. Supported formats: JPEG, PNG, GIF",
    "details": {
      "provided_format": "image/bmp",
      "supported_formats": ["image/jpeg", "image/png", "image/gif"]
    }
  }
}
```

## Security Considerations

- No authentication required (internal API)
- CORS enabled for cross-origin requests
- File type validation for uploads
- Request size limits to prevent abuse
- Timeout mechanisms to prevent resource exhaustion

## Monitoring and Logging

The API provides comprehensive logging:
- Request/response logging with performance metrics
- Error tracking with stack traces
- Memory usage monitoring
- Processing time measurements
- Request ID tracking for debugging
