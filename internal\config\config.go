package config

import (
	"os"
	"gopkg.in/yaml.v3"
)

// Config represents the application configuration
type Config struct {
	Server struct {
		Port         int `yaml:"port"`
		ReadTimeout  int `yaml:"read_timeout"`
		WriteTimeout int `yaml:"write_timeout"`
		EnableCompression bool `yaml:"enable_compression"`
		CompressionLevel  int  `yaml:"compression_level"`
	} `yaml:"server"`
	
	Performance struct {
		EnableCompression bool `yaml:"enable_compression"`
		CompressionLevel  int  `yaml:"compression_level"`
		EnableStreaming   bool `yaml:"enable_streaming"`
		StreamBufferSize  int  `yaml:"stream_buffer_size"`
		MaxStreamMemory   int64 `yaml:"max_stream_memory"`
		EnableMemoryOpt   bool `yaml:"enable_memory_optimization"`
		MemoryLimit       int64 `yaml:"memory_limit"`
		GCInterval        int  `yaml:"gc_interval_seconds"`
		ForceGCThreshold  int64 `yaml:"force_gc_threshold"`
	} `yaml:"performance"`
	
	WorkerPool struct {
		Size           int `yaml:"size"`
		QueueSize      int `yaml:"queue_size"`
		BufferPoolSize int `yaml:"buffer_pool_size"`
	} `yaml:"worker_pool"`
	
	Storage struct {
		LocalPath   string `yaml:"local_path"`
		CloudBucket string `yaml:"cloud_bucket"`
		MaxFileSize int64  `yaml:"max_file_size"`
	} `yaml:"storage"`
	
	CloudStorage struct {
		Enabled   bool   `yaml:"enabled"`
		Bucket    string `yaml:"bucket"`
		Region    string `yaml:"region"`
		AccessKey string `yaml:"access_key"`
		SecretKey string `yaml:"secret_key"`
		Endpoint  string `yaml:"endpoint"` // Optional for custom S3-compatible endpoints
	} `yaml:"cloud_storage"`
	
	Cleanup struct {
		MaxFolders  int `yaml:"max_folders"`  // 1000
		BatchSize   int `yaml:"batch_size"`   // images per folder
		MaxAgeHours int `yaml:"max_age_hours"` // 24
	} `yaml:"cleanup"`
}

// Load reads and parses the configuration file
func Load(filename string) (*Config, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}
	
	var config Config
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, err
	}
	
	return &config, nil
}