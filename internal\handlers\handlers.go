package handlers

import (
	"encoding/json"
	"net/http"

	"qr-background-api/internal/config"
	"qr-background-api/internal/interfaces"
)

// Handlers contains all HTTP handlers for the API
type Handlers struct {
	Upload       *UploadHandler
	QRGeneration *QRGenerationHandler
	Cleanup      *CleanupHandler
	Management   *ManagementHandler
}

// NewHandlers creates a new Handlers instance with all handlers initialized
func NewHandlers(storageManager interfaces.StorageManager, metadataManager interfaces.MetadataManager, workerPool interfaces.WorkerPool, cfg *config.Config) *Handlers {
	return &Handlers{
		Upload:       NewUploadHandler(storageManager, cfg),
		QRGeneration: NewQRGenerationHandler(workerPool, cfg),
		Cleanup:      NewCleanupHandler(metadataManager, storageManager, cfg),
		Management:   NewManagementHandler(metadataManager, storageManager, cfg),
	}
}

// Common response types used across handlers
type SuccessResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

type ErrorResponse struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
	Code    int    `json:"code"`
}

// Common HTTP status codes and error messages
const (
	// Success messages
	MsgUploadSuccess = "Image uploaded successfully"
	MsgQRGenSuccess  = "QR code generated successfully"
	MsgDeleteSuccess = "Image deleted successfully"
	MsgCleanupSuccess = "Cleanup completed successfully"

	// Error messages
	MsgInvalidRequest    = "Invalid request format"
	MsgMethodNotAllowed  = "Method not allowed"
	MsgInternalError     = "Internal server error"
	MsgNotFound          = "Resource not found"
	MsgValidationFailed  = "Validation failed"
)

// WriteJSONResponse writes a JSON response with the given status code
func WriteJSONResponse(w http.ResponseWriter, statusCode int, data interface{}) error {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	return json.NewEncoder(w).Encode(data)
}

// WriteSuccessResponse writes a success response
func WriteSuccessResponse(w http.ResponseWriter, message string, data interface{}) error {
	response := SuccessResponse{
		Success: true,
		Message: message,
		Data:    data,
	}
	return WriteJSONResponse(w, http.StatusOK, response)
}

// WriteErrorResponse writes an error response
func WriteErrorResponse(w http.ResponseWriter, statusCode int, message string) error {
	response := ErrorResponse{
		Success: false,
		Error:   message,
		Code:    statusCode,
	}
	return WriteJSONResponse(w, statusCode, response)
}

// ValidateMethod checks if the request method matches the expected method
func ValidateMethod(w http.ResponseWriter, r *http.Request, expectedMethod string) bool {
	if r.Method != expectedMethod {
		WriteErrorResponse(w, http.StatusMethodNotAllowed, MsgMethodNotAllowed)
		return false
	}
	return true
}

// SetCORSHeaders sets CORS headers for cross-origin requests
func SetCORSHeaders(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
}

// HandlePreflight handles OPTIONS requests for CORS preflight
func HandlePreflight(w http.ResponseWriter, r *http.Request) bool {
	if r.Method == http.MethodOptions {
		SetCORSHeaders(w)
		w.WriteHeader(http.StatusOK)
		return true
	}
	return false
}