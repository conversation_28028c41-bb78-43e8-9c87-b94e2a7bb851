# Health Check Endpoint

Monitor the health and status of the QR Background API service.

## Endpoint Details

- **URL**: `/health`
- **Method**: `GET`
- **Content-Type**: `application/json`
- **Timeout**: 5 seconds

## Request

### Headers
```http
Accept: application/json
```

No request body or parameters required.

### Request Example
```http
GET /health HTTP/1.1
Host: localhost:8080
Accept: application/json
```

## Response

### Success Response (200 OK)
```json
{
  "status": "healthy",
  "timestamp": "2025-01-15T15:30:45Z",
  "version": "1.0.0",
  "uptime_seconds": 3600,
  "checks": {
    "storage": {
      "status": "healthy",
      "message": "Storage accessible",
      "details": {
        "local_path": "./storage",
        "available_space_mb": 15360,
        "total_space_mb": 51200
      }
    },
    "worker_pool": {
      "status": "healthy",
      "message": "Worker pool operational",
      "details": {
        "active_workers": 10,
        "queue_size": 0,
        "processed_jobs": 1250
      }
    },
    "memory": {
      "status": "healthy",
      "message": "Memory usage normal",
      "details": {
        "used_mb": 128,
        "available_mb": 384,
        "gc_runs": 45
      }
    }
  }
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `status` | string | Overall health status (`healthy`, `degraded`, `unhealthy`) |
| `timestamp` | string | ISO 8601 timestamp of health check |
| `version` | string | API version |
| `uptime_seconds` | integer | Service uptime in seconds |
| `checks` | object | Individual component health checks |

### Health Check Components

#### Storage Check
| Field | Type | Description |
|-------|------|-------------|
| `status` | string | Storage health status |
| `message` | string | Human-readable status message |
| `local_path` | string | Local storage path |
| `available_space_mb` | integer | Available disk space in MB |
| `total_space_mb` | integer | Total disk space in MB |

#### Worker Pool Check
| Field | Type | Description |
|-------|------|-------------|
| `status` | string | Worker pool health status |
| `message` | string | Human-readable status message |
| `active_workers` | integer | Number of active workers |
| `queue_size` | integer | Current job queue size |
| `processed_jobs` | integer | Total jobs processed since startup |

#### Memory Check
| Field | Type | Description |
|-------|------|-------------|
| `status` | string | Memory health status |
| `message` | string | Human-readable status message |
| `used_mb` | integer | Current memory usage in MB |
| `available_mb` | integer | Available memory in MB |
| `gc_runs` | integer | Number of garbage collection runs |

## Health Status Values

### Overall Status
- **`healthy`**: All components are functioning normally
- **`degraded`**: Some components have issues but service is operational
- **`unhealthy`**: Critical components are failing

### Component Status
- **`healthy`**: Component is functioning normally
- **`warning`**: Component has minor issues
- **`critical`**: Component has serious issues
- **`unknown`**: Component status cannot be determined

## Error Responses

### 503 Service Unavailable - Unhealthy
```json
{
  "status": "unhealthy",
  "timestamp": "2025-01-15T15:30:45Z",
  "version": "1.0.0",
  "uptime_seconds": 3600,
  "checks": {
    "storage": {
      "status": "critical",
      "message": "Storage not accessible",
      "details": {
        "error": "permission denied: ./storage"
      }
    },
    "worker_pool": {
      "status": "critical",
      "message": "Worker pool not responding",
      "details": {
        "active_workers": 0,
        "queue_size": 25,
        "last_response": "2025-01-15T15:25:00Z"
      }
    },
    "memory": {
      "status": "warning",
      "message": "High memory usage",
      "details": {
        "used_mb": 480,
        "available_mb": 32,
        "gc_runs": 120
      }
    }
  }
}
```

### 500 Internal Server Error
```json
{
  "status": "unknown",
  "timestamp": "2025-01-15T15:30:45Z",
  "error": "Health check failed",
  "details": "internal server error"
}
```

## Examples

### cURL Example
```bash
curl http://localhost:8080/health
```

### JavaScript Example
```javascript
fetch('http://localhost:8080/health')
  .then(response => response.json())
  .then(data => {
    console.log(`Service status: ${data.status}`);
    console.log(`Uptime: ${data.uptime_seconds} seconds`);
    
    // Check individual components
    Object.entries(data.checks).forEach(([component, check]) => {
      console.log(`${component}: ${check.status} - ${check.message}`);
    });
  })
  .catch(error => {
    console.error('Health check failed:', error);
  });
```

### Python Example
```python
import requests
import json

try:
    response = requests.get('http://localhost:8080/health', timeout=5)
    health_data = response.json()
    
    print(f"Service status: {health_data['status']}")
    print(f"Uptime: {health_data['uptime_seconds']} seconds")
    
    # Check individual components
    for component, check in health_data['checks'].items():
        print(f"{component}: {check['status']} - {check['message']}")
        
    if health_data['status'] != 'healthy':
        print("⚠️  Service has health issues!")
        
except requests.exceptions.RequestException as e:
    print(f"❌ Health check failed: {e}")
```

## Monitoring Integration

### Prometheus Metrics
The health endpoint can be used with monitoring systems:

```bash
# Check if service is healthy (exit code 0 = healthy)
curl -f http://localhost:8080/health > /dev/null 2>&1
echo $?  # 0 = healthy, non-zero = unhealthy
```

### Docker Health Check
```dockerfile
HEALTHCHECK --interval=30s --timeout=5s --start-period=10s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1
```

### Kubernetes Liveness Probe
```yaml
livenessProbe:
  httpGet:
    path: /health
    port: 8080
  initialDelaySeconds: 10
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 3
```

## Health Check Logic

### Storage Health
- Checks if storage directory is accessible
- Verifies available disk space
- Tests read/write permissions

### Worker Pool Health
- Verifies workers are active and responding
- Checks queue size for backlog issues
- Monitors job processing rate

### Memory Health
- Monitors current memory usage
- Tracks garbage collection frequency
- Alerts on memory pressure

## Best Practices

### Monitoring Setup
1. **Regular Checks**: Poll health endpoint every 30 seconds
2. **Alerting**: Set up alerts for `degraded` or `unhealthy` status
3. **Logging**: Log health check results for trend analysis
4. **Thresholds**: Define appropriate thresholds for each component

### Response Handling
1. **Status Codes**: Use HTTP status codes for quick health assessment
2. **Component Details**: Check individual component status for debugging
3. **Trending**: Monitor health metrics over time
4. **Automation**: Integrate with deployment and scaling systems

### Troubleshooting
- **503 Response**: Service is unhealthy, check component details
- **Timeout**: Service may be overloaded or unresponsive
- **Connection Refused**: Service is not running
- **High Memory**: Consider restarting or scaling the service
