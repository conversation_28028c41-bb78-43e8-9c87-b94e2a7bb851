# Configuration

Complete configuration guide for the QR Background API server.

## Configuration File

The API is configured via `config.yaml` in the project root directory.

### Default Configuration Location
```
./config.yaml
```

### Environment Override
Configuration can be overridden using environment variables with the prefix `QR_API_`:
```bash
export QR_API_SERVER_PORT=9090
export QR_API_STORAGE_MAX_FILE_SIZE=20971520  # 20MB
```

## Configuration Structure

### Complete Configuration Example
```yaml
server:
  port: 8080
  read_timeout: 30
  write_timeout: 30
  enable_compression: true
  compression_level: 6

performance:
  enable_compression: true
  compression_level: 6
  enable_streaming: true
  stream_buffer_size: 32768      # 32KB
  max_stream_memory: 10485760    # 10MB
  enable_memory_optimization: true
  memory_limit: 536870912        # 512MB
  gc_interval_seconds: 30
  force_gc_threshold: 268435456  # 256MB

worker_pool:
  size: 10
  queue_size: 100
  buffer_pool_size: 50

storage:
  local_path: "./storage"
  cloud_bucket: "qr-background-images"
  max_file_size: 10485760        # 10MB

cloud_storage:
  enabled: false
  bucket: "qr-background-images"
  region: "us-east-1"
  access_key: ""
  secret_key: ""
  endpoint: ""                   # Optional for S3-compatible services

cleanup:
  max_folders: 1000
  batch_size: 100
  max_age_hours: 24

logging:
  level: "info"                  # debug, info, warn, error
  format: "json"                 # json, text
  output: "stdout"               # stdout, stderr, file
  file_path: "./logs/api.log"    # Used when output is "file"
  max_file_size: 100             # MB
  max_backups: 5
  max_age_days: 30

timeouts:
  http_request: 30               # seconds
  file_upload: 60                # seconds
  image_process: 10              # seconds
  qr_generation: 5               # seconds
  storage_op: 20                 # seconds
  cleanup: 120                   # seconds
  health_check: 5                # seconds
  graceful_shutdown: 30          # seconds
```

## Configuration Sections

### Server Configuration

Controls HTTP server behavior and basic settings.

```yaml
server:
  port: 8080                     # HTTP server port
  read_timeout: 30               # Request read timeout (seconds)
  write_timeout: 30              # Response write timeout (seconds)
  enable_compression: true       # Enable gzip compression
  compression_level: 6           # Compression level (1-9)
```

#### Server Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `port` | integer | 8080 | HTTP server listening port |
| `read_timeout` | integer | 30 | Maximum time to read request (seconds) |
| `write_timeout` | integer | 30 | Maximum time to write response (seconds) |
| `enable_compression` | boolean | true | Enable gzip compression for responses |
| `compression_level` | integer | 6 | Gzip compression level (1=fast, 9=best) |

### Performance Configuration

Advanced performance optimization settings.

```yaml
performance:
  enable_compression: true
  compression_level: 6
  enable_streaming: true
  stream_buffer_size: 32768      # 32KB
  max_stream_memory: 10485760    # 10MB
  enable_memory_optimization: true
  memory_limit: 536870912        # 512MB
  gc_interval_seconds: 30
  force_gc_threshold: 268435456  # 256MB
```

#### Performance Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `enable_streaming` | boolean | true | Enable response streaming |
| `stream_buffer_size` | integer | 32768 | Stream buffer size in bytes |
| `max_stream_memory` | integer | 10485760 | Maximum memory for streaming (bytes) |
| `enable_memory_optimization` | boolean | true | Enable automatic memory optimization |
| `memory_limit` | integer | 536870912 | Memory limit in bytes (512MB) |
| `gc_interval_seconds` | integer | 30 | Garbage collection interval |
| `force_gc_threshold` | integer | 268435456 | Force GC threshold in bytes (256MB) |

### Worker Pool Configuration

Controls concurrent QR generation processing.

```yaml
worker_pool:
  size: 10                       # Number of worker goroutines
  queue_size: 100                # Job queue capacity
  buffer_pool_size: 50           # Reusable buffer pool size
```

#### Worker Pool Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `size` | integer | 10 | Number of concurrent workers |
| `queue_size` | integer | 100 | Maximum queued jobs |
| `buffer_pool_size` | integer | 50 | Reusable buffer pool size |

**Sizing Guidelines:**
- **Small Load**: 5-10 workers
- **Medium Load**: 10-20 workers
- **High Load**: 20-50 workers
- **Queue Size**: 5-10x worker count

### Storage Configuration

File storage settings for uploaded images.

```yaml
storage:
  local_path: "./storage"        # Local storage directory
  cloud_bucket: "qr-background-images"  # Cloud storage bucket
  max_file_size: 10485760        # Maximum file size (10MB)
```

#### Storage Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `local_path` | string | "./storage" | Local storage directory path |
| `cloud_bucket` | string | "" | Cloud storage bucket name |
| `max_file_size` | integer | 10485760 | Maximum upload file size (bytes) |

### Cloud Storage Configuration

Optional cloud storage integration (S3-compatible).

```yaml
cloud_storage:
  enabled: false                 # Enable cloud storage
  bucket: "qr-background-images" # S3 bucket name
  region: "us-east-1"           # AWS region
  access_key: ""                # AWS access key
  secret_key: ""                # AWS secret key
  endpoint: ""                  # Custom endpoint (optional)
```

#### Cloud Storage Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `enabled` | boolean | false | Enable cloud storage features |
| `bucket` | string | "" | S3 bucket name |
| `region` | string | "us-east-1" | AWS region |
| `access_key` | string | "" | AWS access key ID |
| `secret_key` | string | "" | AWS secret access key |
| `endpoint` | string | "" | Custom S3-compatible endpoint |

**Supported Providers:**
- Amazon S3
- MinIO
- DigitalOcean Spaces
- Google Cloud Storage (S3-compatible API)

### Cleanup Configuration

Automatic cleanup system settings.

```yaml
cleanup:
  max_folders: 1000              # Maximum folders to track
  batch_size: 100                # Images per folder per cleanup run
  max_age_hours: 24              # Age threshold for cleanup (hours)
```

#### Cleanup Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `max_folders` | integer | 1000 | Maximum folder numbers to track |
| `batch_size` | integer | 100 | Images processed per folder per run |
| `max_age_hours` | integer | 24 | Age threshold for cleanup (hours) |

### Logging Configuration

Logging behavior and output settings.

```yaml
logging:
  level: "info"                  # Log level
  format: "json"                 # Log format
  output: "stdout"               # Output destination
  file_path: "./logs/api.log"    # Log file path
  max_file_size: 100             # Maximum log file size (MB)
  max_backups: 5                 # Number of backup files
  max_age_days: 30               # Maximum age of log files
```

#### Logging Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `level` | string | "info" | Log level (debug, info, warn, error) |
| `format` | string | "json" | Log format (json, text) |
| `output` | string | "stdout" | Output (stdout, stderr, file) |
| `file_path` | string | "./logs/api.log" | Log file path (when output=file) |
| `max_file_size` | integer | 100 | Max log file size in MB |
| `max_backups` | integer | 5 | Number of backup log files |
| `max_age_days` | integer | 30 | Maximum age of log files |

### Timeout Configuration

Operation timeout settings.

```yaml
timeouts:
  http_request: 30               # HTTP request timeout
  file_upload: 60                # File upload timeout
  image_process: 10              # Image processing timeout
  qr_generation: 5               # QR generation timeout
  storage_op: 20                 # Storage operation timeout
  cleanup: 120                   # Cleanup operation timeout
  health_check: 5                # Health check timeout
  graceful_shutdown: 30          # Graceful shutdown timeout
```

#### Timeout Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `http_request` | integer | 30 | General HTTP request timeout (seconds) |
| `file_upload` | integer | 60 | File upload operation timeout |
| `image_process` | integer | 10 | Image processing timeout |
| `qr_generation` | integer | 5 | QR generation timeout |
| `storage_op` | integer | 20 | Storage operation timeout |
| `cleanup` | integer | 120 | Cleanup operation timeout |
| `health_check` | integer | 5 | Health check timeout |
| `graceful_shutdown` | integer | 30 | Server shutdown timeout |

## Environment Variables

Override configuration using environment variables:

```bash
# Server configuration
export QR_API_SERVER_PORT=9090
export QR_API_SERVER_READ_TIMEOUT=45

# Storage configuration
export QR_API_STORAGE_LOCAL_PATH="/data/storage"
export QR_API_STORAGE_MAX_FILE_SIZE=20971520

# Worker pool configuration
export QR_API_WORKER_POOL_SIZE=20
export QR_API_WORKER_POOL_QUEUE_SIZE=200

# Cloud storage
export QR_API_CLOUD_STORAGE_ENABLED=true
export QR_API_CLOUD_STORAGE_ACCESS_KEY="your-access-key"
export QR_API_CLOUD_STORAGE_SECRET_KEY="your-secret-key"
```

### Environment Variable Format
- Prefix: `QR_API_`
- Nested keys: Use underscores (e.g., `server.port` → `QR_API_SERVER_PORT`)
- Boolean values: `true`/`false`
- Numbers: Plain integers

## Configuration Validation

The API validates configuration on startup:

### Required Settings
- `server.port` must be between 1-65535
- `storage.local_path` must be accessible
- `worker_pool.size` must be positive

### Warnings
- Low memory limits may cause performance issues
- Very high worker counts may exhaust resources
- Missing cloud storage credentials when enabled

## Performance Tuning

### High-Performance Setup
```yaml
server:
  port: 8080
  enable_compression: true
  compression_level: 1           # Fast compression

performance:
  enable_streaming: true
  stream_buffer_size: 65536      # 64KB
  memory_limit: 1073741824       # 1GB
  gc_interval_seconds: 15        # More frequent GC

worker_pool:
  size: 20                       # More workers
  queue_size: 200                # Larger queue
  buffer_pool_size: 100          # More buffers

storage:
  max_file_size: 5242880         # 5MB limit for faster processing
```

### Memory-Constrained Setup
```yaml
performance:
  memory_limit: 268435456        # 256MB
  gc_interval_seconds: 10        # Frequent GC
  force_gc_threshold: 134217728  # 128MB

worker_pool:
  size: 5                        # Fewer workers
  queue_size: 25                 # Smaller queue
  buffer_pool_size: 10           # Fewer buffers

storage:
  max_file_size: 2097152         # 2MB limit
```
