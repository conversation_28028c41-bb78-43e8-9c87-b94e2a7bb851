# cURL Examples

Practical cURL examples for all QR Background API endpoints.

## Basic Setup

### Base URL
```bash
BASE_URL="http://localhost:8080"
```

### Common Headers
```bash
CONTENT_TYPE="Content-Type: application/json"
ACCEPT="Accept: application/json"
```

## Upload Examples

### Basic Image Upload
```bash
curl -X POST $BASE_URL/upload \
  -F "file=@background.jpg" \
  -H "Accept: application/json"
```

### Upload with Verbose Output
```bash
curl -X POST $BASE_URL/upload \
  -F "file=@background.jpg" \
  -H "Accept: application/json" \
  -v
```

### Upload Different Image Formats
```bash
# JPEG
curl -X POST $BASE_URL/upload -F "file=@image.jpg"

# PNG
curl -X POST $BASE_URL/upload -F "file=@image.png"

# GIF
curl -X POST $BASE_URL/upload -F "file=@image.gif"
```

### Save Upload Response
```bash
curl -X POST $BASE_URL/upload \
  -F "file=@background.jpg" \
  -H "Accept: application/json" \
  -o upload_response.json
```

## QR Generation Examples

### Basic QR Generation
```bash
curl -X POST $BASE_URL/generate-qr \
  -H "$CONTENT_TYPE" \
  -d '{
    "data": "https://example.com",
    "image_path": "images/347/abc123def456.jpg",
    "x": 100,
    "y": 100,
    "width": 200,
    "height": 200,
    "output_format": "png"
  }' \
  --output generated-qr.png
```

### QR with Different Formats
```bash
# PNG output
curl -X POST $BASE_URL/generate-qr \
  -H "$CONTENT_TYPE" \
  -d '{
    "data": "https://example.com",
    "image_path": "images/347/abc123def456.jpg",
    "x": 50, "y": 50, "width": 150, "height": 150,
    "output_format": "png"
  }' \
  --output qr-code.png

# JPEG output
curl -X POST $BASE_URL/generate-qr \
  -H "$CONTENT_TYPE" \
  -d '{
    "data": "Contact: ******-0123",
    "image_path": "images/347/abc123def456.jpg",
    "x": 200, "y": 200, "width": 300, "height": 300,
    "output_format": "jpeg"
  }' \
  --output qr-code.jpg
```

### QR with Complex Data
```bash
# URL with parameters
curl -X POST $BASE_URL/generate-qr \
  -H "$CONTENT_TYPE" \
  -d '{
    "data": "https://example.com/product?id=123&ref=qr",
    "image_path": "images/347/abc123def456.jpg",
    "x": 100, "y": 100, "width": 200, "height": 200,
    "output_format": "png"
  }' \
  --output product-qr.png

# JSON data
curl -X POST $BASE_URL/generate-qr \
  -H "$CONTENT_TYPE" \
  -d '{
    "data": "{\"name\":\"John Doe\",\"email\":\"<EMAIL>\",\"phone\":\"******-0123\"}",
    "image_path": "images/347/abc123def456.jpg",
    "x": 150, "y": 150, "width": 250, "height": 250,
    "output_format": "png"
  }' \
  --output contact-qr.png

# WiFi configuration
curl -X POST $BASE_URL/generate-qr \
  -H "$CONTENT_TYPE" \
  -d '{
    "data": "WIFI:T:WPA;S:MyNetwork;P:MyPassword;;",
    "image_path": "images/347/abc123def456.jpg",
    "x": 75, "y": 75, "width": 175, "height": 175,
    "output_format": "png"
  }' \
  --output wifi-qr.png
```

### QR with Error Handling
```bash
# Check response status and handle errors
curl -X POST $BASE_URL/generate-qr \
  -H "$CONTENT_TYPE" \
  -d '{
    "data": "https://example.com",
    "image_path": "images/347/abc123def456.jpg",
    "x": 100, "y": 100, "width": 200, "height": 200,
    "output_format": "png"
  }' \
  -w "HTTP Status: %{http_code}\nResponse Time: %{time_total}s\n" \
  --output generated-qr.png \
  --fail-with-body
```

## Management Examples

### List All Images
```bash
curl -X GET $BASE_URL/management/images \
  -H "$ACCEPT"
```

### List with Pagination
```bash
# First page, 10 items
curl -X GET "$BASE_URL/management/images?page=1&limit=10" \
  -H "$ACCEPT"

# Second page, 20 items
curl -X GET "$BASE_URL/management/images?page=2&limit=20" \
  -H "$ACCEPT"
```

### Filter by Folder
```bash
curl -X GET "$BASE_URL/management/images?folder=347" \
  -H "$ACCEPT"
```

### Get Specific Image Info
```bash
curl -X GET $BASE_URL/management/images/abc123def456 \
  -H "$ACCEPT"
```

### Delete Image
```bash
curl -X DELETE $BASE_URL/management/images/abc123def456 \
  -H "$ACCEPT"
```

## Cleanup Examples

### Basic Cleanup
```bash
curl -X POST $BASE_URL/cleanup \
  -H "$CONTENT_TYPE" \
  -d '{}'
```

### Cleanup with Custom Parameters
```bash
curl -X POST $BASE_URL/cleanup \
  -H "$CONTENT_TYPE" \
  -d '{
    "batch_size": 50,
    "max_age_hours": 48
  }'
```

### Cleanup with Detailed Response
```bash
curl -X POST $BASE_URL/cleanup \
  -H "$CONTENT_TYPE" \
  -d '{
    "batch_size": 25,
    "max_age_hours": 24
  }' \
  -w "HTTP Status: %{http_code}\nResponse Time: %{time_total}s\n"
```

## Health Check Examples

### Basic Health Check
```bash
curl -X GET $BASE_URL/health
```

### Health Check with Status Code
```bash
curl -X GET $BASE_URL/health \
  -w "HTTP Status: %{http_code}\n"
```

### Silent Health Check (for monitoring)
```bash
# Returns 0 if healthy, non-zero if unhealthy
curl -f $BASE_URL/health > /dev/null 2>&1
echo "Health check result: $?"
```

## Complete Workflow Examples

### Upload and Generate QR Code
```bash
#!/bin/bash

# Upload image
echo "Uploading background image..."
UPLOAD_RESPONSE=$(curl -s -X POST $BASE_URL/upload \
  -F "file=@background.jpg" \
  -H "Accept: application/json")

# Extract image path
IMAGE_PATH=$(echo $UPLOAD_RESPONSE | grep -o '"image_path":"[^"]*' | cut -d'"' -f4)

if [ -z "$IMAGE_PATH" ]; then
  echo "Upload failed: $UPLOAD_RESPONSE"
  exit 1
fi

echo "Image uploaded: $IMAGE_PATH"

# Generate QR code
echo "Generating QR code..."
curl -X POST $BASE_URL/generate-qr \
  -H "$CONTENT_TYPE" \
  -d "{
    \"data\": \"https://example.com\",
    \"image_path\": \"$IMAGE_PATH\",
    \"x\": 100,
    \"y\": 100,
    \"width\": 200,
    \"height\": 200,
    \"output_format\": \"png\"
  }" \
  --output final-qr.png

if [ $? -eq 0 ]; then
  echo "QR code generated successfully: final-qr.png"
else
  echo "QR generation failed"
  exit 1
fi
```

### Batch QR Generation
```bash
#!/bin/bash

# Upload background once
UPLOAD_RESPONSE=$(curl -s -X POST $BASE_URL/upload \
  -F "file=@background.jpg")
IMAGE_PATH=$(echo $UPLOAD_RESPONSE | grep -o '"image_path":"[^"]*' | cut -d'"' -f4)

# Generate multiple QR codes
declare -a urls=("https://example.com" "https://google.com" "https://github.com")
declare -a positions=("100,100" "300,100" "500,100")

for i in "${!urls[@]}"; do
  url="${urls[$i]}"
  pos="${positions[$i]}"
  x=$(echo $pos | cut -d',' -f1)
  y=$(echo $pos | cut -d',' -f2)
  
  echo "Generating QR for: $url at position ($x,$y)"
  
  curl -X POST $BASE_URL/generate-qr \
    -H "$CONTENT_TYPE" \
    -d "{
      \"data\": \"$url\",
      \"image_path\": \"$IMAGE_PATH\",
      \"x\": $x,
      \"y\": $y,
      \"width\": 150,
      \"height\": 150,
      \"output_format\": \"png\"
    }" \
    --output "qr-$i.png"
done
```

### Monitoring Script
```bash
#!/bin/bash

# Health check with detailed output
echo "=== QR Background API Health Check ==="
HEALTH_RESPONSE=$(curl -s $BASE_URL/health)
STATUS=$(echo $HEALTH_RESPONSE | grep -o '"status":"[^"]*' | cut -d'"' -f4)

echo "Overall Status: $STATUS"

if [ "$STATUS" = "healthy" ]; then
  echo "✅ Service is healthy"
  exit 0
else
  echo "❌ Service has issues"
  echo "Response: $HEALTH_RESPONSE"
  exit 1
fi
```

## Advanced Examples

### Performance Testing
```bash
#!/bin/bash

# Test QR generation performance
echo "Testing QR generation performance..."

for i in {1..10}; do
  start_time=$(date +%s%N)
  
  curl -s -X POST $BASE_URL/generate-qr \
    -H "$CONTENT_TYPE" \
    -d '{
      "data": "Performance test #'$i'",
      "image_path": "images/347/abc123def456.jpg",
      "x": 100, "y": 100, "width": 200, "height": 200,
      "output_format": "png"
    }' \
    --output "perf-test-$i.png"
  
  end_time=$(date +%s%N)
  duration=$(( (end_time - start_time) / 1000000 ))
  
  echo "Request $i: ${duration}ms"
done
```

### Error Handling Examples
```bash
# Test various error conditions

# Invalid image format
curl -X POST $BASE_URL/upload \
  -F "file=@document.pdf" \
  -H "Accept: application/json"

# File too large (if you have a large file)
curl -X POST $BASE_URL/upload \
  -F "file=@large-image.jpg" \
  -H "Accept: application/json"

# Invalid QR parameters
curl -X POST $BASE_URL/generate-qr \
  -H "$CONTENT_TYPE" \
  -d '{
    "data": "",
    "image_path": "nonexistent.jpg",
    "x": -10,
    "y": -10,
    "width": 10,
    "height": 10,
    "output_format": "invalid"
  }'

# Non-existent image
curl -X GET $BASE_URL/management/images/nonexistent123
```
