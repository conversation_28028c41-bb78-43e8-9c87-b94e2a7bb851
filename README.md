# QR Background API

A high-performance Go API for uploading background images and generating QR codes positioned on those backgrounds.

## Project Structure

```
├── cmd/
│   └── server/          # Application entry point
├── internal/
│   ├── config/          # Configuration management
│   └── interfaces/      # Core interfaces
├── pkg/
│   └── utils/           # Shared utilities
├── config.yaml          # Configuration file
└── go.mod              # Go module definition
```

## Getting Started

1. Install dependencies:
   ```bash
   go mod tidy
   ```

2. Run the server:
   ```bash
   go run cmd/server/main.go
   ```

## Configuration

Edit `config.yaml` to customize server settings, worker pool configuration, storage paths, and cleanup parameters.