package qr

import (
	"bytes"
	"fmt"
	"log"
)

// ExampleGenerator_Generate demonstrates basic QR code generation
func ExampleGenerator_Generate() {
	generator := NewGenerator()
	
	// Generate a QR code for a URL
	img, err := generator.Generate("https://example.com", 200, 200)
	if err != nil {
		log.Fatal(err)
	}
	
	// The image can now be used for further processing
	bounds := img.Bounds()
	fmt.Printf("Generated QR code with dimensions: %dx%d\n", bounds.Dx(), bounds.Dy())
	
	// Output: Generated QR code with dimensions: 200x200
}

// ExampleGenerator_GenerateToBuffer demonstrates memory-efficient QR code generation
func ExampleGenerator_GenerateToBuffer() {
	generator := NewGenerator()
	
	// Create a buffer for the QR code
	var buf bytes.Buffer
	
	// Generate QR code directly to buffer
	err := generator.GenerateToBuffer("Hello, World!", 150, 150, &buf)
	if err != nil {
		log.Fatal(err)
	}
	
	fmt.Printf("Generated QR code PNG data: %d bytes\n", buf.Len())
	
	// The buffer now contains PNG-encoded QR code data
	// Output: Generated QR code PNG data: 1057 bytes
}