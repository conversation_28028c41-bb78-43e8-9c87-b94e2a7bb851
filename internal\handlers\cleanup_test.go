package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"qr-background-api/internal/config"
	"qr-background-api/internal/interfaces"
)

// MockMetadataManager implements interfaces.MetadataManager for testing
type MockMetadataManager struct {
	shouldFailGetLastProcessed   bool
	shouldFailSetLastProcessed   bool
	shouldFailGetImagesForCleanup bool
	shouldFailSaveMetadata       bool
	shouldFailDeleteMetadata     bool
	lastProcessedFolder          int
	imagesForCleanup             []interfaces.ImageMetadata
	getLastProcessedError        error
	setLastProcessedError        error
	getImagesError               error
	saveMetadataError            error
	deleteMetadataError          error
}

func (m *MockMetadataManager) SaveImageMetadata(imagePath string, metadata interfaces.ImageMetadata) error {
	if m.shouldFailSaveMetadata {
		return m.saveMetadataError
	}
	return nil
}

func (m *MockMetadataManager) UpdateLastAccess(imagePath string) error {
	return nil
}

func (m *MockMetadataManager) GetImagesForCleanup(folderNum int, maxAge time.Duration, limit int) ([]interfaces.ImageMetadata, error) {
	if m.shouldFailGetImagesForCleanup {
		return nil, m.getImagesError
	}
	return m.imagesForCleanup, nil
}

func (m *MockMetadataManager) DeleteImageMetadata(imagePath string) error {
	if m.shouldFailDeleteMetadata {
		return m.deleteMetadataError
	}
	return nil
}

func (m *MockMetadataManager) GetLastProcessedFolder() (int, error) {
	if m.shouldFailGetLastProcessed {
		return 0, m.getLastProcessedError
	}
	return m.lastProcessedFolder, nil
}

func (m *MockMetadataManager) SetLastProcessedFolder(folderNum int) error {
	if m.shouldFailSetLastProcessed {
		return m.setLastProcessedError
	}
	m.lastProcessedFolder = folderNum
	return nil
}

// MockStorageManagerForCleanup extends MockStorageManager for cleanup testing
type MockStorageManagerForCleanup struct {
	*MockStorageManager
	shouldFailMoveToCloud bool
	shouldFailDeleteImage bool
	moveToCloudError      error
	deleteImageError      error
	movedToCloudPaths     []string
	deletedPaths          []string
}

func (m *MockStorageManagerForCleanup) MoveToCloud(path string) error {
	if m.shouldFailMoveToCloud {
		return m.moveToCloudError
	}
	m.movedToCloudPaths = append(m.movedToCloudPaths, path)
	return nil
}

func (m *MockStorageManagerForCleanup) DeleteImage(path string) error {
	if m.shouldFailDeleteImage {
		return m.deleteImageError
	}
	m.deletedPaths = append(m.deletedPaths, path)
	return nil
}

// Helper function to create test config
func createTestCleanupConfig() *config.Config {
	return &config.Config{
		Cleanup: struct {
			MaxFolders     int `yaml:"max_folders"`
			BatchSize      int `yaml:"batch_size"`
			MaxAgeHours    int `yaml:"max_age_hours"`
		}{
			MaxFolders:  1000,
			BatchSize:   100,
			MaxAgeHours: 24,
		},
	}
}

// Helper function to create test images metadata
func createTestImages() []interfaces.ImageMetadata {
	return []interfaces.ImageMetadata{
		{
			ImagePath:    "images/1/test1.jpg",
			OriginalName: "test1.jpg",
			FileSize:     1024,
			ContentType:  "image/jpeg",
			CreatedAt:    time.Now().Add(-48 * time.Hour),
			LastAccessed: time.Now().Add(-25 * time.Hour),
			Location:     "local",
		},
		{
			ImagePath:    "images/1/test2.png",
			OriginalName: "test2.png",
			FileSize:     2048,
			ContentType:  "image/png",
			CreatedAt:    time.Now().Add(-72 * time.Hour),
			LastAccessed: time.Now().Add(-26 * time.Hour),
			Location:     "cloud",
		},
	}
}

func TestNewCleanupHandler(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
	}

	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	if handler == nil {
		t.Fatal("Expected non-nil handler")
	}
	if handler.metadataManager != mockMetadata {
		t.Error("Expected metadata manager to be set")
	}
	if handler.storageManager != mockStorage {
		t.Error("Expected storage manager to be set")
	}
	if handler.config != cfg {
		t.Error("Expected config to be set")
	}
}

func TestCleanupHandler_ServeHTTP_MethodNotAllowed(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	req := httptest.NewRequest(http.MethodGet, "/cleanup", nil)
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusMethodNotAllowed {
		t.Errorf("Expected status %d, got %d", http.StatusMethodNotAllowed, w.Code)
	}
}

func TestCleanupHandler_ServeHTTP_InvalidJSON(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	req := httptest.NewRequest(http.MethodPost, "/cleanup", strings.NewReader("invalid json"))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
	}
}

func TestCleanupHandler_ServeHTTP_InvalidBatchSize(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	reqBody := CleanupRequest{
		BatchSize: 1001, // Invalid: too large
		MaxAge:    24,
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/cleanup", bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
	}
}

func TestCleanupHandler_ServeHTTP_InvalidMaxAge(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	reqBody := CleanupRequest{
		BatchSize: 50,
		MaxAge:    8761, // Invalid: too large (more than 1 year)
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/cleanup", bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusBadRequest {
		t.Errorf("Expected status %d, got %d", http.StatusBadRequest, w.Code)
	}
}

func TestCleanupHandler_ServeHTTP_SuccessfulCleanup(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{
		lastProcessedFolder: 5,
		imagesForCleanup:    createTestImages(),
	}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
		movedToCloudPaths:  make([]string, 0),
		deletedPaths:       make([]string, 0),
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	reqBody := CleanupRequest{
		BatchSize: 10,
		MaxAge:    24,
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/cleanup", bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response CleanupResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	if !response.Success {
		t.Error("Expected success to be true")
	}
	if response.ProcessedFolder != 6 {
		t.Errorf("Expected processed folder to be 6, got %d", response.ProcessedFolder)
	}
	if response.NextFolder != 7 {
		t.Errorf("Expected next folder to be 7, got %d", response.NextFolder)
	}
	if response.ImagesProcessed != 2 {
		t.Errorf("Expected 2 images processed, got %d", response.ImagesProcessed)
	}
	if response.ImagesMovedToCloud != 1 {
		t.Errorf("Expected 1 image moved to cloud, got %d", response.ImagesMovedToCloud)
	}
	if response.ImagesDeleted != 1 {
		t.Errorf("Expected 1 image deleted, got %d", response.ImagesDeleted)
	}

	// Check that the correct paths were processed
	if len(mockStorage.movedToCloudPaths) != 1 || mockStorage.movedToCloudPaths[0] != "images/1/test1.jpg" {
		t.Error("Expected test1.jpg to be moved to cloud")
	}
	if len(mockStorage.deletedPaths) != 1 || mockStorage.deletedPaths[0] != "images/1/test2.png" {
		t.Error("Expected test2.png to be deleted")
	}
}

func TestCleanupHandler_ServeHTTP_FolderWrapAround(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{
		lastProcessedFolder: 1000, // At max folder
		imagesForCleanup:    []interfaces.ImageMetadata{},
	}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	reqBody := CleanupRequest{
		BatchSize: 10,
		MaxAge:    24,
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/cleanup", bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response CleanupResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Should wrap around to folder 1
	if response.ProcessedFolder != 1 {
		t.Errorf("Expected processed folder to be 1 (wrapped around), got %d", response.ProcessedFolder)
	}
	if response.NextFolder != 2 {
		t.Errorf("Expected next folder to be 2, got %d", response.NextFolder)
	}
}

func TestCleanupHandler_ServeHTTP_MetadataError(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{
		shouldFailGetLastProcessed: true,
		getLastProcessedError:      fmt.Errorf("metadata error"),
	}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	reqBody := CleanupRequest{
		BatchSize: 10,
		MaxAge:    24,
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/cleanup", bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusInternalServerError {
		t.Errorf("Expected status %d, got %d", http.StatusInternalServerError, w.Code)
	}
}

func TestCleanupHandler_ServeHTTP_DefaultValues(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{
		lastProcessedFolder: 0,
		imagesForCleanup:    []interfaces.ImageMetadata{},
	}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	// Send empty request to test default values
	reqBody := CleanupRequest{}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/cleanup", bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	// Should use config defaults and start from folder 1
	var response CleanupResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	if response.ProcessedFolder != 1 {
		t.Errorf("Expected processed folder to be 1, got %d", response.ProcessedFolder)
	}
}

func TestCleanupHandler_ServeHTTP_StorageErrors(t *testing.T) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{
		lastProcessedFolder: 0,
		imagesForCleanup:    createTestImages(),
	}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager:    &MockStorageManager{},
		shouldFailMoveToCloud: true,
		moveToCloudError:      fmt.Errorf("cloud storage error"),
		shouldFailDeleteImage: true,
		deleteImageError:      fmt.Errorf("delete error"),
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	reqBody := CleanupRequest{
		BatchSize: 10,
		MaxAge:    24,
	}
	body, _ := json.Marshal(reqBody)

	req := httptest.NewRequest(http.MethodPost, "/cleanup", bytes.NewReader(body))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response CleanupResponse
	if err := json.NewDecoder(w.Body).Decode(&response); err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Should still process images but mark them as skipped
	if response.ImagesProcessed != 2 {
		t.Errorf("Expected 2 images processed, got %d", response.ImagesProcessed)
	}
	if response.ImagesMovedToCloud != 0 {
		t.Errorf("Expected 0 images moved to cloud due to error, got %d", response.ImagesMovedToCloud)
	}
	if response.ImagesDeleted != 0 {
		t.Errorf("Expected 0 images deleted due to error, got %d", response.ImagesDeleted)
	}

	// Check that all images were marked as skipped
	for _, processedImage := range response.ProcessedImages {
		if processedImage.Action != "skipped" {
			t.Errorf("Expected image %s to be skipped, got action: %s", processedImage.ImagePath, processedImage.Action)
		}
	}
}

// BenchmarkCleanupHandler benchmarks the cleanup handler performance
func BenchmarkCleanupHandler(b *testing.B) {
	cfg := createTestCleanupConfig()
	mockMetadata := &MockMetadataManager{
		lastProcessedFolder: 0,
		imagesForCleanup:    createTestImages(),
	}
	mockStorage := &MockStorageManagerForCleanup{
		MockStorageManager: &MockStorageManager{},
	}
	handler := NewCleanupHandler(mockMetadata, mockStorage, cfg)

	reqBody := CleanupRequest{
		BatchSize: 10,
		MaxAge:    24,
	}
	body, _ := json.Marshal(reqBody)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		req := httptest.NewRequest(http.MethodPost, "/cleanup", bytes.NewReader(body))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			b.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
		}
	}
}