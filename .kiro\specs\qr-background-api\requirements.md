# Requirements Document

## Introduction

This feature involves creating a high-performance Go API that allows users to upload background images and generate QR codes positioned on those backgrounds. The system prioritizes server performance, resource efficiency, and optimal processing speed while providing flexible QR code positioning capabilities.

## Requirements

### Requirement 1

**User Story:** As a developer, I want to upload background images to the server, so that I can use them later for QR code generation.

#### Acceptance Criteria

1. WHEN a user uploads an image file THEN the system SHALL accept common image formats (JPEG, PNG, WebP)
2. WHEN an image is uploaded THEN the system SHALL validate file size limits (max 10MB)
3. WHEN an image is uploaded THEN the system SHALL generate a unique ID without database lookup and return a direct file path that can be used immediately for QR generation
4. WHEN an image is uploaded THEN the system SHALL store the image efficiently with optimized compression
5. IF an invalid image format is uploaded THEN the system SHALL return an appropriate error message

### Requirement 2

**User Story:** As a developer, I want to generate QR codes with custom background images, so that I can create branded QR codes for my applications.

#### Acceptance Criteria

1. WHEN a QR code generation request is made THEN the system SHALL accept QR data content and background image path
2. W<PERSON><PERSON> generating a QR code THEN the system SHALL allow custom positioning (x, y coordinates)
3. WHEN generating a QR code THEN the system SHALL allow custom QR code dimensions (width and height)
4. WHEN generating a QR code THEN the system SHALL directly access background image using provided path without database lookup
5. WHEN generating a QR code THEN the system SHALL validate that QR dimensions fit within the background image boundaries
6. WHEN a QR code is generated THEN the system SHALL return the composite image in the requested format
7. IF an invalid background image path is provided THEN the system SHALL return an appropriate error message
8. IF QR code dimensions exceed background image boundaries THEN the system SHALL return an appropriate error message

### Requirement 3

**User Story:** As a system administrator, I want the API to be highly performant and resource-efficient, so that it can handle high loads without consuming excessive server resources.

#### Acceptance Criteria

1. WHEN processing images THEN the system SHALL use memory-efficient image processing techniques with direct pixel manipulation
2. WHEN multiple requests are made THEN the system SHALL handle concurrent requests efficiently using goroutines
3. WHEN images are stored THEN the system SHALL store images in numbered folders (1-1000) using hash-based distribution of unique IDs for optimal organization and cleanup performance
4. WHEN QR codes are generated THEN the system SHALL optimize processing time to under 80ms per request
5. WHEN combining images THEN the system SHALL use in-memory image composition without temporary files
6. WHEN handling concurrent requests THEN the system SHALL use thread-safe operations and proper synchronization
7. WHEN processing multiple QR generations simultaneously THEN the system SHALL utilize worker pools with memory buffer reuse for optimal resource utilization
8. WHEN generating QR codes THEN the system SHALL process QR generation and background image loading in parallel
9. WHEN compositing images THEN the system SHALL use direct pixel manipulation without intermediate image formats
10. WHEN accessing images for cleanup tracking THEN the system SHALL update last access timestamp in filesystem metadata asynchronously without blocking main process
11. WHEN running cleanup THEN the system SHALL process one numbered folder per cleanup run, scanning images sequentially within that folder
12. WHEN cleanup API is called THEN the system SHALL allow configurable batch size for number of images processed within the current folder
13. WHEN scanning for old files THEN the system SHALL track last processed folder number and resume from the next folder on subsequent cleanup runs, avoiding performance impact on large file collections
14. WHEN processing cleanup THEN the system SHALL use filesystem modification time as fallback if metadata is unavailable to optimize scanning performance
13. WHEN manual cleanup is triggered THEN the system SHALL provide API endpoint to run cleanup with custom parameters
14. WHEN worker pools are initialized THEN the system SHALL pre-allocate memory buffers for QR generation and image processing

### Requirement 4

**User Story:** As a developer, I want comprehensive API endpoints with proper error handling, so that I can integrate the service reliably into my applications.

#### Acceptance Criteria

1. WHEN making API requests THEN the system SHALL provide RESTful endpoints with clear documentation
2. WHEN errors occur THEN the system SHALL return appropriate HTTP status codes and error messages
3. WHEN requests are made THEN the system SHALL validate input parameters and provide meaningful feedback
4. WHEN the API is called THEN the system SHALL implement proper request/response logging
5. WHEN high load occurs THEN the system SHALL handle requests efficiently without artificial rate limiting

### Requirement 5

**User Story:** As a developer, I want to retrieve and manage uploaded background images, so that I can maintain and organize my image assets.

#### Acceptance Criteria

1. WHEN requesting image information for management THEN the system SHALL retrieve metadata from filesystem using unique ID (separate from QR generation flow)
2. WHEN listing images THEN the system SHALL support pagination for large image collections
3. WHEN deleting images THEN the system SHALL remove images from filesystem using unique ID
4. WHEN retrieving images THEN the system SHALL load images from local storage with minimal I/O operations
5. IF an image doesn't exist THEN the system SHALL return a 404 error with clear messaging

### Requirement 6

**User Story:** As a system administrator, I want hybrid storage management, so that frequently used images stay local while unused images are moved to cheaper bucket storage.

#### Acceptance Criteria

1. WHEN background images are not accessed for 24 hours THEN the system SHALL move them from local storage to bucket storage
2. WHEN a QR generation request needs an image not in local storage THEN the system SHALL fetch it from bucket storage and cache locally
3. WHEN tracking image usage THEN the system SHALL store metadata in filesystem for efficient batch processing using unique IDs
4. WHEN cleaning up local storage THEN the system SHALL run background batch cleanup processes without affecting API performance
5. WHEN fetching from bucket THEN the system SHALL implement retry mechanisms for network failures
6. WHEN organizing files THEN the system SHALL implement numbered folder sharding (folders 1-1000) where unique IDs are distributed across folders using hash-based distribution to optimize cleanup scanning performance

### Requirement 7

**User Story:** As a system administrator, I want essential performance optimizations, so that the API can achieve maximum efficiency.

#### Acceptance Criteria

1. WHEN serving responses THEN the system SHALL implement response compression (gzip)
2. WHEN handling file operations THEN the system SHALL use streaming for large files to minimize memory usage
3. WHEN processing images THEN the system SHALL release memory immediately after processing
4. WHEN handling requests THEN the system SHALL implement proper timeout mechanisms
5. WHEN monitoring performance THEN the system SHALL provide basic logging for response times