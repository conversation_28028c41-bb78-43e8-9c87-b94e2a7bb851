package logging

import (
	"bytes"
	"encoding/json"
	"strings"
	"testing"
	"time"
)

func TestConfig_Validation(t *testing.T) {
	tests := []struct {
		name   string
		config Config
		valid  bool
	}{
		{
			name: "valid config",
			config: Config{
				Level:      "info",
				Format:     "json",
				Output:     "stdout",
				Timestamp:  true,
				Caller:     true,
				StackTrace: false,
			},
			valid: true,
		},
		{
			name: "valid text format",
			config: Config{
				Level:      "debug",
				Format:     "text",
				Output:     "stdout",
				Timestamp:  true,
				Caller:     false,
				StackTrace: true,
			},
			valid: true,
		},
		{
			name:   "empty config uses defaults",
			config: Config{},
			valid:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test that config can be used to initialize logger without panicking
			defer func() {
				if r := recover(); r != nil && tt.valid {
					t.<PERSON>("Valid config caused panic: %v", r)
				}
			}()

			InitGlobalLogger(&tt.config)
			logger := GetLogger()

			if logger == nil {
				t.Error("Logger should not be nil")
			}
		})
	}
}

func TestInitGlobalLogger(t *testing.T) {
	// Test JSON format
	var buf bytes.Buffer
	config := Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     true,
		StackTrace: false,
	}

	InitGlobalLogger(&config)
	logger := GetLogger()

	// Redirect output to buffer for testing
	logger.Logger.SetOutput(&buf)

	logger.Info("test message")

	// Check that output is valid JSON
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Errorf("Log output is not valid JSON: %v", err)
	}

	// Check required fields
	if logEntry["message"] != "test message" {
		t.Errorf("Expected message 'test message', got %v", logEntry["message"])
	}
	if logEntry["level"] != "info" {
		t.Errorf("Expected level 'info', got %v", logEntry["level"])
	}
	if _, exists := logEntry["timestamp"]; !exists {
		t.Error("Expected timestamp field")
	}
}

func TestLogger_WithFields(t *testing.T) {
	var buf bytes.Buffer
	config := Config{
		Level:      "debug",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}

	InitGlobalLogger(&config)
	logger := GetLogger()
	logger.Logger.SetOutput(&buf)

	// Test WithFields
	fields := Fields{
		"user_id": "12345",
		"action":  "login",
		"ip":      "***********",
	}

	logger.WithFields(fields).Info("User logged in")

	// Parse JSON output
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Fatalf("Failed to parse log output: %v", err)
	}

	// Check fields
	if logEntry["user_id"] != "12345" {
		t.Errorf("Expected user_id '12345', got %v", logEntry["user_id"])
	}
	if logEntry["action"] != "login" {
		t.Errorf("Expected action 'login', got %v", logEntry["action"])
	}
	if logEntry["ip"] != "***********" {
		t.Errorf("Expected ip '***********', got %v", logEntry["ip"])
	}
	if logEntry["message"] != "User logged in" {
		t.Errorf("Expected message 'User logged in', got %v", logEntry["message"])
	}
}

func TestLogger_WithField(t *testing.T) {
	var buf bytes.Buffer
	config := Config{
		Level:      "debug",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}

	InitGlobalLogger(&config)
	logger := GetLogger()
	logger.Logger.SetOutput(&buf)

	// Test WithField
	logger.WithField("request_id", "req-123").Warn("Request processed")

	// Parse JSON output
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Fatalf("Failed to parse log output: %v", err)
	}

	// Check field
	if logEntry["request_id"] != "req-123" {
		t.Errorf("Expected request_id 'req-123', got %v", logEntry["request_id"])
	}
	if logEntry["level"] != "warning" {
		t.Errorf("Expected level 'warning', got %v", logEntry["level"])
	}
}

func TestLogger_WithError(t *testing.T) {
	var buf bytes.Buffer
	config := Config{
		Level:      "error",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}

	InitGlobalLogger(&config)
	logger := GetLogger()
	logger.Logger.SetOutput(&buf)

	// Test WithError
	testErr := &testError{message: "test error occurred"}
	logger.WithError(testErr).Error("Operation failed")

	// Parse JSON output
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Fatalf("Failed to parse log output: %v", err)
	}

	// Check error field
	if logEntry["error"] != "test error occurred" {
		t.Errorf("Expected error 'test error occurred', got %v", logEntry["error"])
	}
	if logEntry["level"] != "error" {
		t.Errorf("Expected level 'error', got %v", logEntry["level"])
	}
}

func TestLogger_LogLevels(t *testing.T) {
	tests := []struct {
		name     string
		level    string
		logFunc  string
		expected string
	}{
		{
			name:     "debug level",
			level:    "debug",
			logFunc:  "Debug",
			expected: "debug",
		},
		{
			name:     "info level",
			level:    "info",
			logFunc:  "Info",
			expected: "info",
		},
		{
			name:     "warn level",
			level:    "warn",
			logFunc:  "Warn",
			expected: "warning",
		},
		{
			name:     "error level",
			level:    "error",
			logFunc:  "Error",
			expected: "error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			config := Config{
				Level:      "debug", // Set to debug to capture all levels
				Format:     "json",
				Output:     "stdout",
				Timestamp:  true,
				Caller:     false,
				StackTrace: false,
			}

			InitGlobalLogger(&config)
			logger := GetLogger()
			logger.Logger.SetOutput(&buf)

			switch tt.logFunc {
			case "Debug":
				logger.Debug("test message")
			case "Info":
				logger.Info("test message")
			case "Warn":
				logger.Warn("test message")
			case "Error":
				logger.Error("test message")
			}

			// Parse JSON output
			var logEntry map[string]interface{}
			if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
				t.Fatalf("Failed to parse log output: %v", err)
			}

			// Check level
			if logEntry["level"] != tt.expected {
				t.Errorf("Expected level '%s', got %v", tt.expected, logEntry["level"])
			}
		})
	}
}

func TestTextFormat(t *testing.T) {
	var buf bytes.Buffer
	config := Config{
		Level:      "info",
		Format:     "text",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}

	InitGlobalLogger(&config)
	logger := GetLogger()
	logger.Logger.SetOutput(&buf)

	logger.Info("test message")

	output := buf.String()

	// Check that output contains expected elements for text format
	if !strings.Contains(output, "test message") {
		t.Error("Output should contain the log message")
	}
	if !strings.Contains(output, "level=info") {
		t.Error("Output should contain log level")
	}
	// Text format should contain time field
	if !strings.Contains(output, "time=") {
		t.Error("Output should contain timestamp")
	}
}

func TestGetLogger_BeforeInit(t *testing.T) {
	// Reset global logger
	defaultLogger = nil

	// Should not panic and should return a logger
	logger := GetLogger()
	if logger == nil {
		t.Error("GetLogger should not return nil even before initialization")
	}

	// Should be able to log without panicking
	logger.Info("test message")
}

func TestConcurrentAccess(t *testing.T) {
	config := Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}

	InitGlobalLogger(&config)

	// Test concurrent access to logger
	done := make(chan bool, 10)

	for i := 0; i < 10; i++ {
		go func(id int) {
			logger := GetLogger()
			logger.WithField("goroutine", id).Info("concurrent log")
			done <- true
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		select {
		case <-done:
			// Success
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for concurrent logging")
		}
	}
}

// Helper types for testing
type testError struct {
	message string
}

func (e *testError) Error() string {
	return e.message
}

func TestLogMemoryUsage(t *testing.T) {
	var buf bytes.Buffer
	config := Config{
		Level:      "debug",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}

	InitGlobalLogger(&config)
	logger := GetLogger()
	logger.Logger.SetOutput(&buf)

	// Test memory usage logging
	logger.LogMemoryUsage("test_operation", 1024*1024, Fields{
		"component": "test",
	})

	// Parse JSON output
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Fatalf("Failed to parse log output: %v", err)
	}

	// Check required fields
	if logEntry["operation"] != "test_operation" {
		t.Errorf("Expected operation 'test_operation', got %v", logEntry["operation"])
	}

	if logEntry["memory_bytes"] != float64(1024*1024) {
		t.Errorf("Expected memory_bytes %d, got %v", 1024*1024, logEntry["memory_bytes"])
	}

	if logEntry["component"] != "test" {
		t.Errorf("Expected component 'test', got %v", logEntry["component"])
	}
}

func TestLogOperationMetrics(t *testing.T) {
	var buf bytes.Buffer
	config := Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}

	InitGlobalLogger(&config)
	logger := GetLogger()
	logger.Logger.SetOutput(&buf)

	// Test successful operation metrics
	metrics := &OperationMetrics{
		Operation:     "qr_generation",
		Duration:      50 * time.Millisecond,
		MemoryUsed:    2048,
		Success:       true,
		ErrorCount:    0,
		RetryCount:    1,
		ThroughputOps: 20.5,
		CustomFields: map[string]interface{}{
			"qr_size": "256x256",
		},
	}

	logger.LogOperationMetrics(metrics)

	// Parse JSON output
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Fatalf("Failed to parse log output: %v", err)
	}

	// Check required fields
	if logEntry["operation"] != "qr_generation" {
		t.Errorf("Expected operation 'qr_generation', got %v", logEntry["operation"])
	}

	if logEntry["success"] != true {
		t.Errorf("Expected success true, got %v", logEntry["success"])
	}

	if logEntry["qr_size"] != "256x256" {
		t.Errorf("Expected qr_size '256x256', got %v", logEntry["qr_size"])
	}
}

func TestLogOperationMetrics_Failed(t *testing.T) {
	var buf bytes.Buffer
	config := Config{
		Level:      "error",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}

	InitGlobalLogger(&config)
	logger := GetLogger()
	logger.Logger.SetOutput(&buf)

	// Test failed operation metrics
	metrics := &OperationMetrics{
		Operation:     "image_upload",
		Duration:      2 * time.Second,
		MemoryUsed:    1024,
		Success:       false,
		ErrorCount:    3,
		RetryCount:    2,
		ThroughputOps: 0,
	}

	logger.LogOperationMetrics(metrics)

	// Parse JSON output
	var logEntry map[string]interface{}
	if err := json.Unmarshal(buf.Bytes(), &logEntry); err != nil {
		t.Fatalf("Failed to parse log output: %v", err)
	}

	// Check required fields
	if logEntry["operation"] != "image_upload" {
		t.Errorf("Expected operation 'image_upload', got %v", logEntry["operation"])
	}

	if logEntry["success"] != false {
		t.Errorf("Expected success false, got %v", logEntry["success"])
	}

	if logEntry["error_count"] != float64(3) {
		t.Errorf("Expected error_count 3, got %v", logEntry["error_count"])
	}

	// Should log as error level
	if logEntry["level"] != "error" {
		t.Errorf("Expected level 'error', got %v", logEntry["level"])
	}
}
