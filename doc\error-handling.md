# Error Handling

Comprehensive guide to error codes, responses, and troubleshooting for the QR Background API.

## Error Response Format

All API errors follow a consistent JSON structure:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": "Additional error details or object with more information"
  }
}
```

### Error Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Always `false` for error responses |
| `error.code` | string | Machine-readable error code |
| `error.message` | string | Human-readable error description |
| `error.details` | string/object | Additional error context (optional) |

## HTTP Status Codes

| Status Code | Description | When Used |
|-------------|-------------|-----------|
| `400` | Bad Request | Invalid request format, validation errors |
| `404` | Not Found | Resource not found (image, endpoint) |
| `405` | Method Not Allowed | Unsupported HTTP method |
| `408` | Request Timeout | Operation exceeded timeout limit |
| `413` | Payload Too Large | File size exceeds limits |
| `415` | Unsupported Media Type | Invalid file format |
| `500` | Internal Server Error | Server-side processing errors |
| `503` | Service Unavailable | Service is unhealthy or overloaded |

## Error Categories

### 1. Validation Errors (400)

#### INVALID_REQUEST
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid request format or missing required fields",
    "details": "JSON parsing error: unexpected end of input"
  }
}
```

#### VALIDATION_FAILED
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": [
      {
        "field": "data",
        "message": "QR data cannot be empty",
        "value": ""
      },
      {
        "field": "width",
        "message": "QR code size must be between 64 and 2048 pixels",
        "value": 32
      }
    ]
  }
}
```

#### INVALID_IMAGE_FORMAT
```json
{
  "success": false,
  "error": {
    "code": "INVALID_IMAGE_FORMAT",
    "message": "Invalid image format. Supported formats: JPEG, PNG, GIF",
    "details": {
      "provided_format": "image/bmp",
      "supported_formats": ["image/jpeg", "image/png", "image/gif"]
    }
  }
}
```

#### FILE_TOO_LARGE
```json
{
  "success": false,
  "error": {
    "code": "FILE_TOO_LARGE",
    "message": "File size exceeds maximum allowed limit",
    "details": {
      "file_size": 15728640,
      "max_size": 10485760,
      "size_mb": 15.0,
      "max_mb": 10.0
    }
  }
}
```

#### INVALID_QR_SIZE
```json
{
  "success": false,
  "error": {
    "code": "INVALID_QR_SIZE",
    "message": "QR code size must be between 64 and 2048 pixels",
    "details": {
      "provided_size": 32,
      "min_size": 64,
      "max_size": 2048
    }
  }
}
```

#### QR_POSITION_OUT_OF_BOUNDS
```json
{
  "success": false,
  "error": {
    "code": "QR_POSITION_OUT_OF_BOUNDS",
    "message": "QR code position exceeds image boundaries",
    "details": {
      "qr_x": 1500,
      "qr_y": 1000,
      "qr_width": 200,
      "qr_height": 200,
      "image_width": 1200,
      "image_height": 800
    }
  }
}
```

### 2. Not Found Errors (404)

#### IMAGE_NOT_FOUND
```json
{
  "success": false,
  "error": {
    "code": "IMAGE_NOT_FOUND",
    "message": "Background image not found",
    "details": {
      "image_path": "images/347/nonexistent.jpg"
    }
  }
}
```

#### ENDPOINT_NOT_FOUND
```json
{
  "success": false,
  "error": {
    "code": "ENDPOINT_NOT_FOUND",
    "message": "Endpoint not found",
    "details": {
      "path": "/invalid-endpoint",
      "method": "GET"
    }
  }
}
```

### 3. Method Errors (405)

#### METHOD_NOT_ALLOWED
```json
{
  "success": false,
  "error": {
    "code": "METHOD_NOT_ALLOWED",
    "message": "Method not allowed",
    "details": "Only POST method is supported"
  }
}
```

### 4. Timeout Errors (408)

#### QR_PROCESSING_TIMEOUT
```json
{
  "success": false,
  "error": {
    "code": "QR_PROCESSING_TIMEOUT",
    "message": "QR generation processing timeout",
    "details": "Processing exceeded 80ms requirement"
  }
}
```

#### REQUEST_TIMEOUT
```json
{
  "success": false,
  "error": {
    "code": "REQUEST_TIMEOUT",
    "message": "Request processing timeout",
    "details": "Operation exceeded 30 second timeout"
  }
}
```

### 5. Server Errors (500)

#### QR_GENERATION_FAILED
```json
{
  "success": false,
  "error": {
    "code": "QR_GENERATION_FAILED",
    "message": "Failed to generate QR code",
    "details": "invalid QR data encoding"
  }
}
```

#### STORAGE_FAILED
```json
{
  "success": false,
  "error": {
    "code": "STORAGE_FAILED",
    "message": "Failed to store uploaded file",
    "details": "disk space insufficient"
  }
}
```

#### IMAGE_PROCESSING_FAILED
```json
{
  "success": false,
  "error": {
    "code": "IMAGE_PROCESSING_FAILED",
    "message": "Failed to process image",
    "details": "corrupted image data"
  }
}
```

#### WORKER_POOL_ERROR
```json
{
  "success": false,
  "error": {
    "code": "WORKER_POOL_ERROR",
    "message": "Worker pool processing error",
    "details": "all workers are busy"
  }
}
```

#### INSUFFICIENT_MEMORY
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_MEMORY",
    "message": "Insufficient memory for operation",
    "details": {
      "required_mb": 256,
      "available_mb": 128
    }
  }
}
```

#### INTERNAL_SERVER_ERROR
```json
{
  "success": false,
  "error": {
    "code": "INTERNAL_SERVER_ERROR",
    "message": "An unexpected error occurred",
    "details": "contact system administrator"
  }
}
```

## Error Handling Best Practices

### Client-Side Error Handling

#### JavaScript Example
```javascript
async function handleApiCall(url, options) {
  try {
    const response = await fetch(url, options);
    const data = await response.json();
    
    if (!response.ok) {
      // Handle HTTP error status
      throw new ApiError(data.error.code, data.error.message, response.status);
    }
    
    if (!data.success) {
      // Handle API error response
      throw new ApiError(data.error.code, data.error.message);
    }
    
    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      // Handle known API errors
      console.error(`API Error [${error.code}]: ${error.message}`);
    } else {
      // Handle network or other errors
      console.error('Network error:', error.message);
    }
    throw error;
  }
}

class ApiError extends Error {
  constructor(code, message, status) {
    super(message);
    this.code = code;
    this.status = status;
    this.name = 'ApiError';
  }
}
```

#### Python Example
```python
import requests
from typing import Dict, Any

class ApiError(Exception):
    def __init__(self, code: str, message: str, status: int = None, details: Any = None):
        super().__init__(message)
        self.code = code
        self.message = message
        self.status = status
        self.details = details

def handle_api_response(response: requests.Response) -> Dict[str, Any]:
    try:
        data = response.json()
    except ValueError:
        raise ApiError("INVALID_RESPONSE", "Invalid JSON response", response.status_code)
    
    if not response.ok:
        error = data.get('error', {})
        raise ApiError(
            error.get('code', 'UNKNOWN_ERROR'),
            error.get('message', 'Unknown error occurred'),
            response.status_code,
            error.get('details')
        )
    
    if not data.get('success', True):
        error = data.get('error', {})
        raise ApiError(
            error.get('code', 'API_ERROR'),
            error.get('message', 'API operation failed'),
            details=error.get('details')
        )
    
    return data

# Usage example
try:
    response = requests.post('http://localhost:8080/upload', files={'file': open('image.jpg', 'rb')})
    data = handle_api_response(response)
    print(f"Upload successful: {data['image_path']}")
except ApiError as e:
    print(f"API Error [{e.code}]: {e.message}")
    if e.details:
        print(f"Details: {e.details}")
```

### Retry Logic

#### Exponential Backoff
```javascript
async function apiCallWithRetry(url, options, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await handleApiCall(url, options);
    } catch (error) {
      if (error.code === 'QR_PROCESSING_TIMEOUT' && attempt < maxRetries) {
        // Retry timeout errors with exponential backoff
        const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      throw error;
    }
  }
}
```

## Troubleshooting Guide

### Common Issues and Solutions

#### Upload Failures
- **FILE_TOO_LARGE**: Reduce file size or increase `max_file_size` config
- **INVALID_IMAGE_FORMAT**: Ensure file is JPEG, PNG, or GIF
- **STORAGE_FAILED**: Check disk space and permissions

#### QR Generation Failures
- **QR_PROCESSING_TIMEOUT**: Reduce image size or increase worker pool
- **IMAGE_NOT_FOUND**: Verify image path from upload response
- **QR_POSITION_OUT_OF_BOUNDS**: Adjust QR position and size

#### Performance Issues
- **REQUEST_TIMEOUT**: Increase timeout configuration
- **WORKER_POOL_ERROR**: Increase worker pool size
- **INSUFFICIENT_MEMORY**: Increase memory limits or optimize usage

### Debugging Tips

1. **Check Request ID**: Use `X-Request-ID` header for log correlation
2. **Validate Input**: Ensure all required fields are provided
3. **Monitor Logs**: Check server logs for detailed error information
4. **Test Endpoints**: Use health check to verify service status
5. **Network Issues**: Verify connectivity and firewall settings

### Logging and Monitoring

The API provides detailed logging for all errors:
- Request/response details
- Stack traces for server errors
- Performance metrics
- Error frequency and patterns

Monitor these metrics to identify and resolve issues proactively.
