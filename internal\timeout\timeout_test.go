package timeout

import (
	"context"
	"errors"
	"sync"
	"testing"
	"time"
)

func TestNewManager(t *testing.T) {
	config := &Config{
		HTTPRequest:      30 * time.Second,
		FileUpload:       60 * time.Second,
		ImageProcess:     10 * time.Second,
		QRGeneration:     5 * time.Second,
		StorageOp:        20 * time.Second,
		Cleanup:          120 * time.Second,
		HealthCheck:      5 * time.Second,
		GracefulShutdown: 30 * time.Second,
	}

	manager := NewManager(config, nil)

	if manager == nil {
		t.Fatal("NewManager should not return nil")
	}

	if manager.GetConfig().HTTPRequest != config.HTTPRequest {
		t.<PERSON>rf("Expected HTTPRequest timeout %v, got %v", config.HTTPRequest, manager.GetConfig().HTTPRequest)
	}
	if manager.GetConfig().FileUpload != config.FileUpload {
		t.Errorf("Expected FileUpload timeout %v, got %v", config.FileUpload, manager.GetConfig().FileUpload)
	}
	if manager.GetConfig().ImageProcess != config.ImageProcess {
		t.Errorf("Expected ImageProcess timeout %v, got %v", config.ImageProcess, manager.GetConfig().ImageProcess)
	}
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()

	// Check that all timeouts are set to reasonable defaults
	if config.HTTPRequest <= 0 {
		t.Error("HTTPRequest timeout should be positive")
	}
	if config.FileUpload <= 0 {
		t.Error("FileUpload timeout should be positive")
	}
	if config.ImageProcess <= 0 {
		t.Error("ImageProcess timeout should be positive")
	}
	if config.QRGeneration <= 0 {
		t.Error("QRGeneration timeout should be positive")
	}
	if config.StorageOp <= 0 {
		t.Error("StorageOp timeout should be positive")
	}
	if config.Cleanup <= 0 {
		t.Error("Cleanup timeout should be positive")
	}
	if config.HealthCheck <= 0 {
		t.Error("HealthCheck timeout should be positive")
	}
	if config.GracefulShutdown <= 0 {
		t.Error("GracefulShutdown timeout should be positive")
	}
}

func TestManager_WithTimeout_Success(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test successful execution within timeout
	err := manager.WithTimeout(ctx, 100*time.Millisecond, "test_operation", func(ctx context.Context) error {
		time.Sleep(10 * time.Millisecond) // Short operation
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestManager_WithTimeout_Timeout(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test timeout scenario
	err := manager.WithTimeout(ctx, 50*time.Millisecond, "test_operation", func(ctx context.Context) error {
		time.Sleep(100 * time.Millisecond) // Long operation
		return nil
	})

	if err == nil {
		t.Error("Expected timeout error")
	}
}

func TestManager_WithTimeout_FunctionError(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	testErr := errors.New("function error")

	// Test function returning error
	err := manager.WithTimeout(ctx, 100*time.Millisecond, "test_operation", func(ctx context.Context) error {
		return testErr
	})

	if err != testErr {
		t.Errorf("Expected function error, got %v", err)
	}
}

func TestManager_WithTimeout_ContextCancellation(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx, cancel := context.WithCancel(context.Background())

	// Cancel context after short delay
	go func() {
		time.Sleep(25 * time.Millisecond)
		cancel()
	}()

	// Test context cancellation
	err := manager.WithTimeout(ctx, 100*time.Millisecond, "test_operation", func(ctx context.Context) error {
		time.Sleep(50 * time.Millisecond)
		return nil
	})

	if err == nil {
		t.Error("Expected cancellation error")
	}
}

func TestManager_ExecuteWithRetry_Success(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test successful execution on first try
	err := manager.ExecuteWithRetry(ctx, 100*time.Millisecond, "test_operation", 3, func(ctx context.Context) error {
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestManager_ExecuteWithRetry_SuccessAfterRetries(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	attempts := 0
	testErr := errors.New("temporary error")

	// Test successful execution after retries
	err := manager.ExecuteWithRetry(ctx, 200*time.Millisecond, "test_operation", 3, func(ctx context.Context) error {
		attempts++
		if attempts < 3 {
			return testErr
		}
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	if attempts != 3 {
		t.Errorf("Expected 3 attempts, got %d", attempts)
	}
}

func TestManager_ExecuteWithRetry_MaxRetriesExceeded(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	attempts := 0
	testErr := errors.New("persistent error")

	// Test max retries exceeded
	err := manager.ExecuteWithRetry(ctx, 200*time.Millisecond, "test_operation", 2, func(ctx context.Context) error {
		attempts++
		return testErr
	})

	if err != testErr {
		t.Errorf("Expected persistent error, got %v", err)
	}
	if attempts != 3 { // Initial attempt + 2 retries
		t.Errorf("Expected 3 attempts (1 initial + 2 retries), got %d", attempts)
	}
}

func TestManager_ExecuteConcurrent_Success(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test concurrent execution
	operations := map[string]func(context.Context) error{
		"op1": func(ctx context.Context) error {
			time.Sleep(10 * time.Millisecond)
			return nil
		},
		"op2": func(ctx context.Context) error {
			time.Sleep(15 * time.Millisecond)
			return nil
		},
		"op3": func(ctx context.Context) error {
			time.Sleep(5 * time.Millisecond)
			return nil
		},
	}

	results := manager.ExecuteConcurrent(ctx, 100*time.Millisecond, operations)

	if len(results) != 3 {
		t.Errorf("Expected 3 results, got %d", len(results))
	}

	// Check that all operations completed successfully
	for opName, err := range results {
		if err != nil {
			t.Errorf("Expected no error for operation %s, got %v", opName, err)
		}
	}
}

func TestManager_ExecuteConcurrent_WithErrors(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	testErr := errors.New("function error")

	// Test concurrent execution with some errors
	operations := map[string]func(context.Context) error{
		"success_op": func(ctx context.Context) error {
			return nil
		},
		"error_op": func(ctx context.Context) error {
			return testErr
		},
		"another_success_op": func(ctx context.Context) error {
			return nil
		},
	}

	results := manager.ExecuteConcurrent(ctx, 100*time.Millisecond, operations)

	if len(results) != 3 {
		t.Errorf("Expected 3 results, got %d", len(results))
	}

	// Check that we have both successful and error results
	successCount := 0
	errorCount := 0

	for opName, err := range results {
		if err != nil {
			errorCount++
			if err != testErr {
				t.Errorf("Expected test error for operation %s, got %v", opName, err)
			}
		} else {
			successCount++
		}
	}

	if successCount != 2 {
		t.Errorf("Expected 2 successful results, got %d", successCount)
	}
	if errorCount != 1 {
		t.Errorf("Expected 1 error result, got %d", errorCount)
	}
}

func TestConvenienceFunctions(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test convenience functions
	tests := []struct {
		name string
		fn   func(context.Context, func(context.Context) error) error
	}{
		{"WithHTTPTimeout", manager.WithHTTPTimeout},
		{"WithFileUploadTimeout", manager.WithFileUploadTimeout},
		{"WithImageProcessTimeout", manager.WithImageProcessTimeout},
		{"WithQRGenerationTimeout", manager.WithQRGenerationTimeout},
		{"WithDatabaseQueryTimeout", manager.WithDatabaseQueryTimeout},
		{"WithStorageOpTimeout", manager.WithStorageOpTimeout},
		{"WithCleanupTimeout", manager.WithCleanupTimeout},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.fn(ctx, func(ctx context.Context) error {
				return nil
			})

			if err != nil {
				t.Errorf("Expected no error, got %v", err)
			}
		})
	}
}

func TestGlobalManager(t *testing.T) {
	config := Config{
		HTTP:          25 * time.Second,
		FileUpload:    55 * time.Second,
		ImageProcess:  8 * time.Second,
		QRGeneration:  4 * time.Second,
		DatabaseQuery: 12 * time.Second,
		StorageOp:     18 * time.Second,
		Cleanup:       100 * time.Second,
	}

	// Test global manager initialization
	InitGlobalManager(config)
	manager := GetManager()

	if manager == nil {
		t.Fatal("GetManager should not return nil")
	}

	if manager.Config.HTTP != config.HTTP {
		t.Errorf("Expected HTTP timeout %v, got %v", config.HTTP, manager.Config.HTTP)
	}
}

func TestGlobalManager_BeforeInit(t *testing.T) {
	// Reset global manager
	globalManager = nil

	// Should not panic and should return a manager with default config
	manager := GetManager()
	if manager == nil {
		t.Error("GetManager should not return nil even before initialization")
	}

	// Should have default timeouts
	defaultConfig := DefaultConfig()
	if manager.Config.HTTP != defaultConfig.HTTP {
		t.Errorf("Expected default HTTP timeout %v, got %v", defaultConfig.HTTP, manager.Config.HTTP)
	}
}

func TestConcurrentManagerAccess(t *testing.T) {
	config := DefaultConfig()
	InitGlobalManager(config)

	// Test concurrent access to global manager
	var wg sync.WaitGroup
	results := make(chan bool, 10)

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			manager := GetManager()
			ctx := context.Background()

			_, err := manager.WithTimeout(ctx, 50*time.Millisecond, func() (interface{}, error) {
				time.Sleep(10 * time.Millisecond)
				return "concurrent result", nil
			})

			results <- (err == nil)
		}()
	}

	wg.Wait()
	close(results)

	// Check all operations succeeded
	for success := range results {
		if !success {
			t.Error("Concurrent operation failed")
		}
	}
}

func TestManager_WithFileUploadTimeout(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test successful file upload operation
	err := manager.WithFileUploadTimeout(ctx, "test_upload", func(ctx context.Context) error {
		time.Sleep(10 * time.Millisecond)
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestManager_WithFileUploadTimeout_Timeout(t *testing.T) {
	config := DefaultConfig()
	config.FileUpload = 50 * time.Millisecond
	manager := NewManager(config, nil)
	ctx := context.Background()

	// Test timeout scenario
	err := manager.WithFileUploadTimeout(ctx, "test_upload", func(ctx context.Context) error {
		time.Sleep(100 * time.Millisecond) // Longer than timeout
		return nil
	})

	if err == nil {
		t.Error("Expected timeout error")
	}
}

func TestManager_WithImageProcessTimeout(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test successful image processing operation
	err := manager.WithImageProcessTimeout(ctx, "test_process", func(ctx context.Context) error {
		time.Sleep(5 * time.Millisecond)
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestManager_WithImageProcessTimeout_Timeout(t *testing.T) {
	config := DefaultConfig()
	config.ImageProcess = 30 * time.Millisecond
	manager := NewManager(config, nil)
	ctx := context.Background()

	// Test timeout scenario
	err := manager.WithImageProcessTimeout(ctx, "test_process", func(ctx context.Context) error {
		time.Sleep(60 * time.Millisecond) // Longer than timeout
		return nil
	})

	if err == nil {
		t.Error("Expected timeout error")
	}
}

func TestManager_WithQRGenerationTimeout(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test successful QR generation operation
	err := manager.WithQRGenerationTimeout(ctx, "test_qr", func(ctx context.Context) error {
		time.Sleep(2 * time.Millisecond)
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestManager_WithQRGenerationTimeout_Timeout(t *testing.T) {
	config := DefaultConfig()
	config.QRGeneration = 20 * time.Millisecond
	manager := NewManager(config, nil)
	ctx := context.Background()

	// Test timeout scenario
	err := manager.WithQRGenerationTimeout(ctx, "test_qr", func(ctx context.Context) error {
		time.Sleep(40 * time.Millisecond) // Longer than timeout
		return nil
	})

	if err == nil {
		t.Error("Expected timeout error")
	}
}

func TestManager_WithStorageOpTimeout(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test successful storage operation
	err := manager.WithStorageOpTimeout(ctx, "test_storage", func(ctx context.Context) error {
		time.Sleep(5 * time.Millisecond)
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestManager_WithCleanupTimeout(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test successful cleanup operation
	err := manager.WithCleanupTimeout(ctx, "test_cleanup", func(ctx context.Context) error {
		time.Sleep(10 * time.Millisecond)
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestManager_WithHealthCheckTimeout(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test successful health check operation
	err := manager.WithHealthCheckTimeout(ctx, "test_health", func(ctx context.Context) error {
		time.Sleep(1 * time.Millisecond)
		return nil
	})

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
}

func TestManager_PerformanceMetrics(t *testing.T) {
	manager := NewManager(DefaultConfig(), nil)
	ctx := context.Background()

	// Test that performance metrics are logged
	start := time.Now()
	err := manager.WithTimeout(ctx, 100*time.Millisecond, "performance_test", func(ctx context.Context) error {
		time.Sleep(10 * time.Millisecond)
		return nil
	})
	duration := time.Since(start)

	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}

	// Should complete within reasonable time
	if duration > 50*time.Millisecond {
		t.Errorf("Operation took too long: %v", duration)
	}
}
