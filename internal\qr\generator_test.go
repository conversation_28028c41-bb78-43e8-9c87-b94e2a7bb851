package qr

import (
	"bytes"
	"fmt"
	"image/png"
	"testing"
)

func TestNewGenerator(t *testing.T) {
	generator := NewGenerator()
	if generator == nil {
		t.<PERSON><PERSON>("NewGenerator() returned nil")
	}
	
	// Verify it implements the interface
	_, ok := generator.(*Generator)
	if !ok {
		t.<PERSON>al("NewGenerator() did not return a *Generator")
	}
}

func TestGenerator_Generate(t *testing.T) {
	generator := NewGenerator()
	
	tests := []struct {
		name        string
		data        string
		width       int
		height      int
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid QR code generation",
			data:        "https://example.com",
			width:       200,
			height:      200,
			expectError: false,
		},
		{
			name:        "valid QR code with different dimensions",
			data:        "Hello, World!",
			width:       150,
			height:      150,
			expectError: false,
		},
		{
			name:        "valid QR code with rectangular dimensions",
			data:        "test data",
			width:       300,
			height:      200,
			expectError: false,
		},
		{
			name:        "empty data",
			data:        "",
			width:       200,
			height:      200,
			expectError: true,
			errorMsg:    "QR data cannot be empty",
		},
		{
			name:        "zero width",
			data:        "test",
			width:       0,
			height:      200,
			expectError: true,
			errorMsg:    "QR dimensions must be positive",
		},
		{
			name:        "zero height",
			data:        "test",
			width:       200,
			height:      0,
			expectError: true,
			errorMsg:    "QR dimensions must be positive",
		},
		{
			name:        "negative width",
			data:        "test",
			width:       -100,
			height:      200,
			expectError: true,
			errorMsg:    "QR dimensions must be positive",
		},
		{
			name:        "negative height",
			data:        "test",
			width:       200,
			height:      -100,
			expectError: true,
			errorMsg:    "QR dimensions must be positive",
		},
		{
			name:        "very large QR code",
			data:        "Large QR code test",
			width:       1000,
			height:      1000,
			expectError: false,
		},
		{
			name:        "very small QR code",
			data:        "Small",
			width:       50,
			height:      50,
			expectError: false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			img, err := generator.Generate(tt.data, tt.width, tt.height)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errorMsg != "" && err.Error() != tt.errorMsg {
					// For dimension errors, check if the error contains the expected message
					if tt.errorMsg == "QR dimensions must be positive" {
						if err.Error()[:len(tt.errorMsg)] != tt.errorMsg {
							t.Errorf("Expected error message to start with %q, got %q", tt.errorMsg, err.Error())
						}
					} else if err.Error() != tt.errorMsg {
						t.Errorf("Expected error message %q, got %q", tt.errorMsg, err.Error())
					}
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			
			if img == nil {
				t.Error("Generated image is nil")
				return
			}
			
			// Verify the image has reasonable bounds
			bounds := img.Bounds()
			if bounds.Dx() <= 0 || bounds.Dy() <= 0 {
				t.Errorf("Generated image has invalid bounds: %v", bounds)
			}
		})
	}
}

func TestGenerator_GenerateToBuffer(t *testing.T) {
	generator := NewGenerator()
	
	tests := []struct {
		name        string
		data        string
		width       int
		height      int
		buffer      *bytes.Buffer
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid buffer generation",
			data:        "https://example.com",
			width:       200,
			height:      200,
			buffer:      &bytes.Buffer{},
			expectError: false,
		},
		{
			name:        "valid buffer with different data",
			data:        "Hello, World!",
			width:       150,
			height:      150,
			buffer:      &bytes.Buffer{},
			expectError: false,
		},
		{
			name:        "nil buffer",
			data:        "test",
			width:       200,
			height:      200,
			buffer:      nil,
			expectError: true,
			errorMsg:    "buffer cannot be nil",
		},
		{
			name:        "empty data with buffer",
			data:        "",
			width:       200,
			height:      200,
			buffer:      &bytes.Buffer{},
			expectError: true,
			errorMsg:    "failed to generate QR code: QR data cannot be empty",
		},
		{
			name:        "invalid dimensions with buffer",
			data:        "test",
			width:       0,
			height:      200,
			buffer:      &bytes.Buffer{},
			expectError: true,
			errorMsg:    "failed to generate QR code: QR dimensions must be positive",
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := generator.GenerateToBuffer(tt.data, tt.width, tt.height, tt.buffer)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if tt.errorMsg != "" {
					// For wrapped errors, check if the error contains the expected message
					if tt.errorMsg == "failed to generate QR code: QR dimensions must be positive" {
						if len(err.Error()) < len(tt.errorMsg) || err.Error()[:len(tt.errorMsg)] != tt.errorMsg {
							t.Errorf("Expected error message to start with %q, got %q", tt.errorMsg, err.Error())
						}
					} else if err.Error() != tt.errorMsg {
						t.Errorf("Expected error message %q, got %q", tt.errorMsg, err.Error())
					}
				}
				return
			}
			
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}
			
			if tt.buffer.Len() == 0 {
				t.Error("Buffer is empty after generation")
				return
			}
			
			// Verify the buffer contains valid PNG data by trying to decode it
			img, err := png.Decode(bytes.NewReader(tt.buffer.Bytes()))
			if err != nil {
				t.Errorf("Failed to decode generated PNG: %v", err)
				return
			}
			
			if img == nil {
				t.Error("Decoded image is nil")
				return
			}
			
			// Verify the image has reasonable bounds
			bounds := img.Bounds()
			if bounds.Dx() <= 0 || bounds.Dy() <= 0 {
				t.Errorf("Decoded image has invalid bounds: %v", bounds)
			}
		})
	}
}

func TestGenerator_GenerateVariousDataTypes(t *testing.T) {
	generator := NewGenerator()
	
	testData := []string{
		"https://example.com",
		"mailto:<EMAIL>",
		"tel:+1234567890",
		"Simple text",
		"UTF-8 text: 你好世界",
		"Numbers: 1234567890",
		"Special chars: !@#$%^&*()",
		"Long text: " + string(make([]byte, 1000)), // Very long string
	}
	
	for _, data := range testData {
		t.Run("data_type_"+data[:min(20, len(data))], func(t *testing.T) {
			img, err := generator.Generate(data, 200, 200)
			if err != nil {
				t.Errorf("Failed to generate QR for data %q: %v", data[:min(50, len(data))], err)
				return
			}
			
			if img == nil {
				t.Error("Generated image is nil")
			}
		})
	}
}

func TestGenerator_GenerateVariousDimensions(t *testing.T) {
	generator := NewGenerator()
	data := "test data"
	
	dimensions := []struct {
		width, height int
	}{
		{50, 50},
		{100, 100},
		{200, 200},
		{300, 300},
		{500, 500},
		{100, 200}, // Rectangular
		{200, 100}, // Rectangular
		{1, 1},     // Minimum size
	}
	
	for _, dim := range dimensions {
		t.Run(fmt.Sprintf("dimensions_%dx%d", dim.width, dim.height), func(t *testing.T) {
			img, err := generator.Generate(data, dim.width, dim.height)
			if err != nil {
				t.Errorf("Failed to generate QR with dimensions %dx%d: %v", dim.width, dim.height, err)
				return
			}
			
			if img == nil {
				t.Error("Generated image is nil")
			}
		})
	}
}

func TestGenerator_BufferReuse(t *testing.T) {
	generator := NewGenerator()
	
	// Test that the same buffer can be reused multiple times
	buf := &bytes.Buffer{}
	
	for i := 0; i < 5; i++ {
		buf.Reset() // Clear the buffer
		
		data := fmt.Sprintf("test data %d", i)
		err := generator.GenerateToBuffer(data, 200, 200, buf)
		if err != nil {
			t.Errorf("Failed to generate QR to buffer on iteration %d: %v", i, err)
			return
		}
		
		if buf.Len() == 0 {
			t.Errorf("Buffer is empty after generation on iteration %d", i)
			return
		}
		
		// Verify the buffer contains valid PNG data
		_, err = png.Decode(bytes.NewReader(buf.Bytes()))
		if err != nil {
			t.Errorf("Failed to decode generated PNG on iteration %d: %v", i, err)
			return
		}
	}
}

// Helper function for minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}