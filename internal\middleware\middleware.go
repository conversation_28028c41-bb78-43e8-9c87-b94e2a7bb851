package middleware

import (
	"compress/gzip"
	"net/http"
	"time"
)

// MiddlewareStack combines all performance optimization middlewares
type MiddlewareStack struct {
	compression *CompressionMiddleware
	streaming   *StreamingMiddleware
	memory      *MemoryMiddleware
}

// NewMiddlewareStack creates a new middleware stack with all optimizations
func NewMiddlewareStack() *MiddlewareStack {
	return &MiddlewareStack{
		compression: NewCompressionMiddleware(gzip.DefaultCompression),
		streaming:   NewStreamingMiddleware(32*1024, 10*1024*1024), // 32KB buffer, 10MB max
		memory:      NewMemoryMiddleware(512, 30*time.Second, 0.8), // 512MB, 30s, 80% threshold
	}
}

// NewCustomMiddlewareStack creates a middleware stack with custom settings
func NewCustomMiddlewareStack(compressionLevel int, bufferSize int, maxMemoryMB int64, gcInterval time.Duration) *MiddlewareStack {
	return &MiddlewareStack{
		compression: NewCompressionMiddleware(compressionLevel),
		streaming:   NewStreamingMiddleware(bufferSize, maxMemoryMB*1024*1024),
		memory:      NewMemoryMiddleware(maxMemoryMB, gcInterval, 0.8),
	}
}

// Apply applies all middlewares to the given handler in the correct order
func (ms *MiddlewareStack) Apply(handler http.Handler) http.Handler {
	// Apply middlewares in reverse order (last applied = first executed)
	// Order: Memory -> Streaming -> Compression -> Handler
	handler = ms.memory.Handler(handler)
	handler = ms.streaming.Handler(handler)
	handler = ms.compression.Handler(handler)
	return handler
}

// ApplyToHandlerFunc applies all middlewares to a handler function
func (ms *MiddlewareStack) ApplyToHandlerFunc(handlerFunc http.HandlerFunc) http.Handler {
	return ms.Apply(handlerFunc)
}

// GetCompressionMiddleware returns the compression middleware
func (ms *MiddlewareStack) GetCompressionMiddleware() *CompressionMiddleware {
	return ms.compression
}

// GetStreamingMiddleware returns the streaming middleware
func (ms *MiddlewareStack) GetStreamingMiddleware() *StreamingMiddleware {
	return ms.streaming
}

// GetMemoryMiddleware returns the memory middleware
func (ms *MiddlewareStack) GetMemoryMiddleware() *MemoryMiddleware {
	return ms.memory
}

// PerformanceMiddleware is a convenience function that creates and applies all performance middlewares
func PerformanceMiddleware(handler http.Handler) http.Handler {
	stack := NewMiddlewareStack()
	return stack.Apply(handler)
}

// PerformanceMiddlewareFunc is a convenience function for handler functions
func PerformanceMiddlewareFunc(handlerFunc http.HandlerFunc) http.Handler {
	stack := NewMiddlewareStack()
	return stack.ApplyToHandlerFunc(handlerFunc)
}



// loggingResponseWriter wraps http.ResponseWriter to capture response data
type loggingResponseWriter struct {
	http.ResponseWriter
	statusCode   int
	bytesWritten int64
	headerWritten bool
}

// Write captures bytes written for logging
func (lrw *loggingResponseWriter) Write(data []byte) (int, error) {
	if !lrw.headerWritten {
		lrw.WriteHeader(http.StatusOK)
	}
	n, err := lrw.ResponseWriter.Write(data)
	lrw.bytesWritten += int64(n)
	return n, err
}

// WriteHeader captures status code for logging
func (lrw *loggingResponseWriter) WriteHeader(statusCode int) {
	if lrw.headerWritten {
		return
	}
	lrw.headerWritten = true
	lrw.statusCode = statusCode
	lrw.ResponseWriter.WriteHeader(statusCode)
}