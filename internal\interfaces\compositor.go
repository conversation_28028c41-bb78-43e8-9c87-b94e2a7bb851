package interfaces

import (
	"bytes"
	"image"
)

// ImageCompositor handles combining QR codes with background images
type ImageCompositor interface {
	// Composite combines a QR code with a background image at specified coordinates
	Composite(background image.Image, qr image.Image, x, y int) (image.Image, error)
	
	// CompositeToBuffer combines images and writes the result directly to a buffer
	CompositeToBuffer(background image.Image, qr image.Image, x, y int, buf *bytes.Buffer, format string) error
}