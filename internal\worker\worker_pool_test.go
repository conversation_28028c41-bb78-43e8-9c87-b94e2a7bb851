package worker

import (
	"fmt"
	"sync"
	"testing"
	"time"

	"qr-background-api/internal/interfaces"
)

func TestBufferPool(t *testing.T) {
	bp := NewBufferPool(10, 5, 1024)

	t.Run("GetAndReturnQRBuffer", func(t *testing.T) {
		buf := bp.GetQRBuffer()
		if buf == nil {
			t.<PERSON>r("Expected non-nil buffer")
		}
		bp.ReturnQRBuffer(buf)
	})

	t.Run("GetAndReturnImageBuffer", func(t *testing.T) {
		buf := bp.GetImageBuffer()
		if buf == nil {
			t.Error("Expected non-nil buffer")
		}
		bp.ReturnImageBuffer(buf)
	})

	t.Run("BufferPoolStats", func(t *testing.T) {
		stats := bp.GetStats()
		if stats.QRBuffersInUse < 0 || stats.ImageBuffersInUse < 0 {
			t.<PERSON>("Invalid buffer counts")
		}
	})

	t.Run("ConcurrentAccess", func(t *testing.T) {
		var wg sync.WaitGroup
		for i := 0; i < 10; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				buf := bp.GetQRBuffer()
				bp.ReturnQRBuffer(buf)
			}()
		}
		wg.Wait()
	})
}

func TestWorkerPool(t *testing.T) {
	t.Run("StartAndStop", func(t *testing.T) {
		qrGenerator := &mockQRGenerator{}
		compositor := &mockCompositor{}
		storageManager := &mockStorageManager{}

		config := interfaces.WorkerConfig{
			WorkerCount:     2,
			QueueSize:       10,
			JobTimeout:      5 * time.Second,
			ShutdownTimeout: 2 * time.Second,
		}

		bufferPool := NewBufferPool(10, 5, 1024)
		wp := NewWorkerPool(config, bufferPool, qrGenerator, compositor, storageManager)

		err := wp.Start(2)
		if err != nil {
			t.Fatalf("Failed to start worker pool: %v", err)
		}

		if !wp.IsRunning() {
			t.Error("Worker pool should be running")
		}

		err = wp.Stop()
		if err != nil {
			t.Fatalf("Failed to stop worker pool: %v", err)
		}

		if wp.IsRunning() {
			t.Error("Worker pool should not be running")
		}
	})

	t.Run("SimpleJobProcessing", func(t *testing.T) {
		qrGenerator := &mockQRGenerator{}
		compositor := &mockCompositor{}
		storageManager := &mockStorageManager{}

		config := interfaces.WorkerConfig{
			WorkerCount:     2,
			QueueSize:       10,
			JobTimeout:      5 * time.Second,
			ShutdownTimeout: 2 * time.Second,
		}

		bufferPool := NewBufferPool(10, 5, 1024)
		wp := NewWorkerPool(config, bufferPool, qrGenerator, compositor, storageManager)

		err := wp.Start(1)
		if err != nil {
			t.Fatalf("Failed to start worker pool: %v", err)
		}
		defer wp.Stop()

		job := interfaces.QRJob{
			ID: "test-job",
			Request: interfaces.QRRequest{
				Data:         "test data",
				ImagePath:    "test-path",
				X:            10,
				Y:            10,
				Width:        100,
				Height:       100,
				OutputFormat: "png",
			},
			Timeout: 1 * time.Second,
		}

		resultChan := wp.Submit(job)

		select {
		case result := <-resultChan:
			if result.Error != nil {
				t.Fatalf("Job failed: %v", result.Error)
			}
			if len(result.ImageData) == 0 {
				t.Error("Expected non-empty image data")
			}
			if result.JobID != job.ID {
				t.Error("Job ID mismatch")
			}
		case <-time.After(2 * time.Second):
			t.Fatal("Job processing timeout")
		}
	})

	t.Run("WorkerPoolStats", func(t *testing.T) {
		qrGenerator := &mockQRGenerator{}
		compositor := &mockCompositor{}
		storageManager := &mockStorageManager{}

		config := interfaces.WorkerConfig{
			WorkerCount:     2,
			QueueSize:       10,
			JobTimeout:      5 * time.Second,
			ShutdownTimeout: 2 * time.Second,
		}

		bufferPool := NewBufferPool(10, 5, 1024)
		wp := NewWorkerPool(config, bufferPool, qrGenerator, compositor, storageManager)

		stats := wp.GetStats()
		if stats.ActiveWorkers < 0 {
			t.Error("Invalid worker count")
		}
	})
}

func BenchmarkWorkerPool(b *testing.B) {
	qrGenerator := &mockQRGenerator{}
	compositor := &mockCompositor{}
	storageManager := &mockStorageManager{}

	config := interfaces.WorkerConfig{
		WorkerCount:     4,
		QueueSize:       100,
		JobTimeout:      5 * time.Second,
		ShutdownTimeout: 2 * time.Second,
	}

	bufferPool := NewBufferPool(10, 5, 1024)
	wp := NewWorkerPool(config, bufferPool, qrGenerator, compositor, storageManager)
	err := wp.Start(4)
	if err != nil {
		b.Fatalf("Failed to start worker pool: %v", err)
	}
	defer wp.Stop()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			job := interfaces.QRJob{
				ID: fmt.Sprintf("bench-job-%d", b.N),
				Request: interfaces.QRRequest{
					Data:         "benchmark data",
					ImagePath:    "test-path",
					X:            10,
					Y:            10,
					Width:        100,
					Height:       100,
					OutputFormat: "png",
				},
				Timeout: 1 * time.Second,
			}

			resultChan := wp.Submit(job)
			<-resultChan
		}
	})
}