package worker

import (
	"bytes"
	"image"
	"mime/multipart"
	"time"
)

// Mock implementations for testing
type mockQRGenerator struct{}

func (m *mockQRGenerator) Generate(data string, width, height int) (image.Image, error) {
	// Create a simple test image
	img := image.NewRGBA(image.Rect(0, 0, width, height))
	return img, nil
}

func (m *mockQRGenerator) GenerateToBuffer(data string, width, height int, buf *bytes.Buffer) error {
	// Write some test QR data to buffer
	buf.WriteString("mock-qr-data")
	return nil
}

type mockCompositor struct{}

func (m *mockCompositor) Composite(background image.Image, qr image.Image, x, y int) (image.Image, error) {
	return background, nil
}

func (m *mockCompositor) CompositeToBuffer(background image.Image, qr image.Image, x, y int, buf *bytes.Buffer, format string) error {
	// Write some test data to buffer
	buf.WriteString("composite-image-data")
	return nil
}

type mockStorageManager struct{}

func (m *mockStorageManager) SaveImage(file multipart.File) (string, error) {
	return "test-path", nil
}

func (m *mockStorageManager) LoadImage(path string) (image.Image, error) {
	// Create a simple test background image
	img := image.NewRGBA(image.Rect(0, 0, 200, 200))
	return img, nil
}

func (m *mockStorageManager) DeleteImage(path string) error {
	return nil
}

func (m *mockStorageManager) MoveToCloud(localPath string) error {
	return nil
}

func (m *mockStorageManager) FetchFromCloud(cloudPath string) error {
	return nil
}

func (m *mockStorageManager) GenerateUniqueID() string {
	return "test-id"
}

func (m *mockStorageManager) GetFolderFromID(id string) int {
	return 1
}

// slowMockQRGenerator simulates slow QR generation for timeout testing
type slowMockQRGenerator struct {
	delay time.Duration
}

func (s *slowMockQRGenerator) Generate(data string, width, height int) (image.Image, error) {
	time.Sleep(s.delay)
	mock := &mockQRGenerator{}
	return mock.Generate(data, width, height)
}

func (s *slowMockQRGenerator) GenerateToBuffer(data string, width, height int, buf *bytes.Buffer) error {
	time.Sleep(s.delay)
	mock := &mockQRGenerator{}
	return mock.GenerateToBuffer(data, width, height, buf)
}