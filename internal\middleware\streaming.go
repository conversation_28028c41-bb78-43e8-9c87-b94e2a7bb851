package middleware

import (
	"bufio"
	"io"
	"net/http"
	"strconv"
)

// StreamingMiddleware provides streaming support for large file operations
type StreamingMiddleware struct {
	bufferSize int
	maxMemory  int64
}

// NewStreamingMiddleware creates a new streaming middleware
func NewStreamingMiddleware(bufferSize int, maxMemory int64) *StreamingMiddleware {
	if bufferSize <= 0 {
		bufferSize = 32 * 1024 // 32KB default buffer
	}
	if maxMemory <= 0 {
		maxMemory = 10 * 1024 * 1024 // 10MB default max memory
	}
	return &StreamingMiddleware{
		bufferSize: bufferSize,
		maxMemory:  maxMemory,
	}
}

// streamingResponseWriter wraps http.ResponseWriter to provide streaming capabilities
type streamingResponseWriter struct {
	http.ResponseWriter
	bufferSize int
	flusher    http.Flusher
	headerWritten bool
}

// Write writes data in chunks to enable streaming
func (srw *streamingResponseWriter) Write(data []byte) (int, error) {
	if !srw.headerWritten {
		srw.WriteHeader(http.StatusOK)
	}
	
	totalWritten := 0
	for len(data) > 0 {
		chunkSize := srw.bufferSize
		if len(data) < chunkSize {
			chunkSize = len(data)
		}
		
		n, err := srw.ResponseWriter.Write(data[:chunkSize])
		totalWritten += n
		if err != nil {
			return totalWritten, err
		}
		
		// Flush after each chunk for streaming
		if srw.flusher != nil {
			srw.flusher.Flush()
		}
		
		data = data[chunkSize:]
	}
	
	return totalWritten, nil
}

// WriteHeader sets the response status code and streaming headers
func (srw *streamingResponseWriter) WriteHeader(statusCode int) {
	if srw.headerWritten {
		return
	}
	srw.headerWritten = true
	
	// Set headers for streaming
	srw.Header().Set("Transfer-Encoding", "chunked")
	srw.Header().Set("Cache-Control", "no-cache")
	
	srw.ResponseWriter.WriteHeader(statusCode)
}

// Handler returns an HTTP handler that enables streaming for large responses
func (sm *StreamingMiddleware) Handler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check if response should be streamed
		if !sm.shouldStream(r) {
			next.ServeHTTP(w, r)
			return
		}
		
		// Get flusher interface if available
		flusher, _ := w.(http.Flusher)
		
		// Wrap response writer with streaming capabilities
		srw := &streamingResponseWriter{
			ResponseWriter: w,
			bufferSize:     sm.bufferSize,
			flusher:        flusher,
		}
		
		// Call next handler with streaming response writer
		next.ServeHTTP(srw, r)
	})
}

// shouldStream determines if the request should use streaming based on various factors
func (sm *StreamingMiddleware) shouldStream(r *http.Request) bool {
	// Stream for image generation requests (potentially large responses)
	if r.URL.Path == "/generate-qr" {
		return true
	}
	
	// Stream for image retrieval requests
	if r.URL.Path == "/images" && r.Method == "GET" {
		return true
	}
	
	// Don't stream for small API responses
	if r.Header.Get("Accept") == "application/json" {
		return false
	}
	
	return false
}

// StreamFile streams a file to the response with proper headers and chunking
func (sm *StreamingMiddleware) StreamFile(w http.ResponseWriter, r *http.Request, reader io.Reader, contentType string, contentLength int64) error {
	// Set appropriate headers
	w.Header().Set("Content-Type", contentType)
	if contentLength > 0 {
		w.Header().Set("Content-Length", strconv.FormatInt(contentLength, 10))
	}
	
	// Enable range requests for large files
	if contentLength > sm.maxMemory {
		w.Header().Set("Accept-Ranges", "bytes")
	}
	
	// Handle range requests
	if rangeHeader := r.Header.Get("Range"); rangeHeader != "" && contentLength > 0 {
		return sm.handleRangeRequest(w, r, reader, contentLength, rangeHeader)
	}
	
	// Stream the entire file
	return sm.streamContent(w, reader)
}

// streamContent streams content using buffered I/O
func (sm *StreamingMiddleware) streamContent(w http.ResponseWriter, reader io.Reader) error {
	buffer := make([]byte, sm.bufferSize)
	bufReader := bufio.NewReaderSize(reader, sm.bufferSize)
	
	for {
		n, err := bufReader.Read(buffer)
		if n > 0 {
			if _, writeErr := w.Write(buffer[:n]); writeErr != nil {
				return writeErr
			}
			
			// Flush for streaming
			if flusher, ok := w.(http.Flusher); ok {
				flusher.Flush()
			}
		}
		
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
	}
	
	return nil
}

// handleRangeRequest handles HTTP range requests for partial content
func (sm *StreamingMiddleware) handleRangeRequest(w http.ResponseWriter, r *http.Request, reader io.Reader, contentLength int64, rangeHeader string) error {
	// For simplicity, this implementation doesn't parse range headers
	// In a production system, you would parse "bytes=start-end" format
	// and seek to the appropriate position in the reader
	
	// For now, just stream the entire content
	return sm.streamContent(w, reader)
}

// HandlerFunc returns a handler function that enables streaming
func (sm *StreamingMiddleware) HandlerFunc(next http.HandlerFunc) http.HandlerFunc {
	return sm.Handler(next).ServeHTTP
}

// DefaultStreamingMiddleware creates a streaming middleware with default settings
func DefaultStreamingMiddleware() *StreamingMiddleware {
	return NewStreamingMiddleware(32*1024, 10*1024*1024) // 32KB buffer, 10MB max memory
}