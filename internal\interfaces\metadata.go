package interfaces

import (
	"time"
)

// ImageMetadata represents metadata for stored images
type ImageMetadata struct {
	ImagePath    string    `json:"image_path"`
	OriginalName string    `json:"original_name"`
	FileSize     int64     `json:"file_size"`
	ContentType  string    `json:"content_type"`
	CreatedAt    time.Time `json:"created_at"`
	LastAccessed time.Time `json:"last_accessed"`
	Location     string    `json:"location"` // "local" or "cloud"
}

// MetadataManager handles filesystem-based metadata operations
type MetadataManager interface {
	// SaveImageMetadata saves metadata for an uploaded image
	SaveImageMetadata(imagePath string, metadata ImageMetadata) error
	
	// UpdateLastAccess updates the last access timestamp for an image
	UpdateLastAccess(imagePath string) error
	
	// GetImagesForCleanup retrieves images from a specific folder that are eligible for cleanup
	GetImagesForCleanup(folderNum int, maxAge time.Duration, limit int) ([]ImageMetadata, error)
	
	// DeleteImageMetadata removes metadata for a deleted image
	DeleteImageMetadata(imagePath string) error
	
	// GetLastProcessedFolder returns the last folder number processed during cleanup
	GetLastProcessedFolder() (int, error)
	
	// SetLastProcessedFolder updates the last processed folder number
	SetLastProcessedFolder(folderNum int) error
}