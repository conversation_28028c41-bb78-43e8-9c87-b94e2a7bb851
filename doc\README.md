# QR Background API Documentation

A high-performance Go API for uploading background images and generating QR codes positioned on those backgrounds.

## 📚 Documentation Structure

This documentation is organized into the following sections:

- **[API Overview](./api-overview.md)** - General information about the API
- **[Authentication](./authentication.md)** - Authentication and security details
- **[Endpoints](./endpoints/)** - Detailed endpoint documentation
  - [Upload Endpoint](./endpoints/upload.md)
  - [QR Generation Endpoint](./endpoints/qr-generation.md)
  - [Cleanup Endpoint](./endpoints/cleanup.md)
  - [Management Endpoints](./endpoints/management.md)
  - [Health Check](./endpoints/health.md)
- **[Error Handling](./error-handling.md)** - Error codes and responses
- **[Configuration](./configuration.md)** - Server configuration options
- **[Examples](./examples/)** - Code examples and use cases
  - [cURL Examples](./examples/curl-examples.md)
  - [JavaScript Examples](./examples/javascript-examples.md)
  - [Python Examples](./examples/python-examples.md)
- **[Performance](./performance.md)** - Performance characteristics and optimization
- **[Deployment](./deployment.md)** - Deployment and infrastructure guide

## 🚀 Quick Start

### Base URL
```
http://localhost:8080
```

### Key Features
- **Fast QR Generation**: Sub-80ms processing time requirement
- **Multiple Image Formats**: Support for JPEG, PNG, GIF
- **Concurrent Processing**: Worker pool for parallel QR generation
- **Automatic Cleanup**: Configurable cleanup system for old images
- **Memory Optimization**: Built-in memory management and streaming
- **Comprehensive Logging**: Structured logging with performance metrics

### Basic Usage Flow

1. **Upload Background Image**
   ```bash
   curl -X POST http://localhost:8080/upload \
     -F "file=@background.jpg"
   ```

2. **Generate QR Code**
   ```bash
   curl -X POST http://localhost:8080/generate-qr \
     -H "Content-Type: application/json" \
     -d '{
       "data": "https://example.com",
       "image_path": "images/123/abc123def456.jpg",
       "x": 100,
       "y": 100,
       "width": 200,
       "height": 200,
       "output_format": "png"
     }'
   ```

3. **List Images**
   ```bash
   curl http://localhost:8080/management/images
   ```

## 📋 Requirements

- Go 1.19 or higher
- Minimum 512MB RAM
- Storage space for uploaded images
- Optional: Cloud storage (S3-compatible)

## 🔧 Configuration

The API is configured via `config.yaml`. See [Configuration Guide](./configuration.md) for detailed options.

## 📊 Performance

- **QR Generation**: < 80ms processing time
- **Concurrent Requests**: Configurable worker pool (default: 10 workers)
- **File Upload**: Up to 10MB per file (configurable)
- **Memory Usage**: Optimized with automatic garbage collection

## 🛠️ Development

For development setup and contribution guidelines, see the main project README.

## 📞 Support

For issues and questions:
- Check the [Error Handling Guide](./error-handling.md)
- Review [Examples](./examples/) for common use cases
- Examine server logs for detailed error information

---

**Version**: 1.0.0  
**Last Updated**: 2025-01-15
