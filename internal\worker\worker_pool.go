package worker

import (
	"bytes"
	"context"
	"fmt"
	"image"
	"sync"
	"sync/atomic"
	"time"

	"qr-background-api/internal/interfaces"
)

// workerPool implements the WorkerPool interface
type workerPool struct {
	// Configuration
	config interfaces.WorkerConfig
	
	// Worker management
	workers    []*worker
	workerWg   sync.WaitGroup
	jobQueue   chan interfaces.QRJob
	quitChan   chan struct{}
	running    int32 // atomic flag
	
	// Dependencies
	bufferPool   interfaces.BufferPool
	qrGenerator  interfaces.QRGenerator
	compositor   interfaces.ImageCompositor
	storageManager interfaces.StorageManager
	
	// Statistics (atomic counters)
	completedJobs int64
	failedJobs    int64
	totalLatency  int64 // in nanoseconds
	
	// Synchronization
	mu sync.RWMutex
}

// worker represents a single worker in the pool
type worker struct {
	id           int
	pool         *workerPool
	quitChan     chan struct{}
	processing   int32 // atomic flag
}

// NewWorkerPool creates a new worker pool with dependencies
func NewWorkerPool(
	config interfaces.WorkerConfig,
	bufferPool interfaces.BufferPool,
	qrGenerator interfaces.QRGenerator,
	compositor interfaces.ImageCompositor,
	storageManager interfaces.StorageManager,
) interfaces.WorkerPool {
	return &workerPool{
		config:         config,
		bufferPool:     bufferPool,
		qrGenerator:    qrGenerator,
		compositor:     compositor,
		storageManager: storageManager,
		jobQueue:       make(chan interfaces.QRJob, config.QueueSize),
		quitChan:       make(chan struct{}),
	}
}

// Start initializes and starts the worker pool with specified worker count
func (wp *workerPool) Start(workerCount int) error {
	wp.mu.Lock()
	defer wp.mu.Unlock()
	
	if atomic.LoadInt32(&wp.running) == 1 {
		return fmt.Errorf("worker pool is already running")
	}
	
	if workerCount <= 0 {
		workerCount = wp.config.WorkerCount
	}
	
	// Create workers
	wp.workers = make([]*worker, workerCount)
	for i := 0; i < workerCount; i++ {
		wp.workers[i] = &worker{
			id:       i,
			pool:     wp,
			quitChan: make(chan struct{}),
		}
	}
	
	// Start workers
	for _, w := range wp.workers {
		wp.workerWg.Add(1)
		go w.start()
	}
	
	atomic.StoreInt32(&wp.running, 1)
	return nil
}

// Stop gracefully shuts down the worker pool
func (wp *workerPool) Stop() error {
	wp.mu.Lock()
	defer wp.mu.Unlock()
	
	if atomic.LoadInt32(&wp.running) == 0 {
		return fmt.Errorf("worker pool is not running")
	}
	
	// Set running to false first
	atomic.StoreInt32(&wp.running, 0)
	
	// Signal all workers to stop
	select {
	case <-wp.quitChan:
		// Already closed
	default:
		close(wp.quitChan)
	}
	
	// Stop individual workers
	for _, w := range wp.workers {
		select {
		case <-w.quitChan:
			// Already closed
		default:
			close(w.quitChan)
		}
	}
	
	// Wait for workers to finish with timeout
	done := make(chan struct{})
	go func() {
		wp.workerWg.Wait()
		close(done)
	}()
	
	select {
	case <-done:
		// All workers stopped gracefully
	case <-time.After(wp.config.ShutdownTimeout):
		// Timeout reached, force shutdown
		return fmt.Errorf("worker pool shutdown timeout")
	}
	
	return nil
}

// Submit submits a job to the worker pool and returns a channel for the result
func (wp *workerPool) Submit(job interfaces.QRJob) <-chan interfaces.QRResult {
	resultChan := make(chan interfaces.QRResult, 1)
	
	if atomic.LoadInt32(&wp.running) == 0 {
		resultChan <- interfaces.QRResult{
			JobID: job.ID,
			Error: fmt.Errorf("worker pool is not running"),
		}
		return resultChan
	}
	
	// Set result channel
	job.ResultChan = resultChan
	
	// Set default timeout if not specified
	if job.Timeout == 0 {
		job.Timeout = 5 * time.Second // Default timeout
	}
	
	// Try to submit job to queue
	select {
	case wp.jobQueue <- job:
		// Job submitted successfully
	case <-wp.quitChan:
		// Worker pool is shutting down
		resultChan <- interfaces.QRResult{
			JobID: job.ID,
			Error: fmt.Errorf("worker pool is shutting down"),
		}
	case <-time.After(100 * time.Millisecond):
		// Queue is full, reject job
		resultChan <- interfaces.QRResult{
			JobID: job.ID,
			Error: fmt.Errorf("job queue is full"),
		}
	}
	
	return resultChan
}

// GetStats returns current worker pool statistics
func (wp *workerPool) GetStats() interfaces.WorkerPoolStats {
	wp.mu.RLock()
	defer wp.mu.RUnlock()
	
	activeWorkers := 0
	for _, w := range wp.workers {
		if atomic.LoadInt32(&w.processing) == 1 {
			activeWorkers++
		}
	}
	
	completedJobs := atomic.LoadInt64(&wp.completedJobs)
	totalLatency := atomic.LoadInt64(&wp.totalLatency)
	
	var avgLatency time.Duration
	if completedJobs > 0 {
		avgLatency = time.Duration(totalLatency / completedJobs)
	}
	
	return interfaces.WorkerPoolStats{
		ActiveWorkers:   activeWorkers,
		QueuedJobs:      len(wp.jobQueue),
		CompletedJobs:   completedJobs,
		FailedJobs:      atomic.LoadInt64(&wp.failedJobs),
		AverageLatency:  avgLatency,
		BufferPoolStats: wp.bufferPool.GetStats(),
	}
}

// IsRunning returns true if the worker pool is currently running
func (wp *workerPool) IsRunning() bool {
	return atomic.LoadInt32(&wp.running) == 1
}

// start begins the worker's job processing loop
func (w *worker) start() {
	defer w.pool.workerWg.Done()
	
	for {
		select {
		case job := <-w.pool.jobQueue:
			w.processJob(job)
		case <-w.quitChan:
			return
		case <-w.pool.quitChan:
			return
		}
	}
}

// processJob processes a single QR generation job with timeout and context support
func (w *worker) processJob(job interfaces.QRJob) {
	atomic.StoreInt32(&w.processing, 1)
	defer atomic.StoreInt32(&w.processing, 0)
	
	startTime := time.Now()
	result := interfaces.QRResult{
		JobID: job.ID,
	}
	
	defer func() {
		if r := recover(); r != nil {
			result.Error = fmt.Errorf("panic in worker: %v", r)
		}
		
		result.Duration = time.Since(startTime)
		
		// Update statistics
		if result.Error != nil {
			atomic.AddInt64(&w.pool.failedJobs, 1)
		} else {
			atomic.AddInt64(&w.pool.completedJobs, 1)
			atomic.AddInt64(&w.pool.totalLatency, int64(result.Duration))
		}
		
		// Send result safely with timeout
		if job.ResultChan != nil {
			select {
			case job.ResultChan <- result:
				// Result sent successfully
			case <-time.After(100 * time.Millisecond):
				// Timeout sending result - receiver may have given up
			case <-w.quitChan:
				// Worker is shutting down
			case <-w.pool.quitChan:
				// Pool is shutting down
			}
		}
	}()
	
	// Set up job timeout if not provided
	if job.Timeout == 0 {
		job.Timeout = w.pool.config.JobTimeout
		if job.Timeout == 0 {
			job.Timeout = 5 * time.Second // Default timeout
		}
	}
	
	// Create context with timeout for the entire job
	ctx := job.Context
	if ctx == nil {
		ctx = context.Background()
	}
	
	jobCtx, cancel := context.WithTimeout(ctx, job.Timeout)
	defer cancel()
	
	// Check for initial cancellation
	select {
	case <-jobCtx.Done():
		result.Error = fmt.Errorf("job cancelled before processing: %w", jobCtx.Err())
		return
	case <-w.quitChan:
		result.Error = fmt.Errorf("worker shutdown before processing")
		return
	case <-w.pool.quitChan:
		result.Error = fmt.Errorf("worker pool shutdown before processing")
		return
	default:
		// Continue processing
	}
	
	// Get buffers from pool with timeout
	bufferTimeout := time.NewTimer(1 * time.Second)
	defer bufferTimeout.Stop()
	
	var qrBuf, imageBuf *bytes.Buffer
	
	// Get QR buffer
	select {
	case <-jobCtx.Done():
		result.Error = fmt.Errorf("job cancelled while getting QR buffer: %w", jobCtx.Err())
		return
	default:
		qrBuf = w.pool.bufferPool.GetQRBuffer()
		defer w.pool.bufferPool.ReturnQRBuffer(qrBuf)
	}
	
	// Get image buffer
	select {
	case <-jobCtx.Done():
		result.Error = fmt.Errorf("job cancelled while getting image buffer: %w", jobCtx.Err())
		return
	default:
		imageBuf = w.pool.bufferPool.GetImageBuffer()
		defer w.pool.bufferPool.ReturnImageBuffer(imageBuf)
	}
	
	// Process the job with context
	processDone := make(chan struct{})
	go func() {
		defer close(processDone)
		result.ImageData, result.Error = w.generateQRWithBackgroundContext(jobCtx, job.Request, qrBuf, imageBuf)
	}()
	
	// Wait for processing to complete or timeout
	select {
	case <-processDone:
		// Processing completed
	case <-jobCtx.Done():
		result.Error = fmt.Errorf("job processing timeout: %w", jobCtx.Err())
	case <-w.quitChan:
		result.Error = fmt.Errorf("worker shutdown during processing")
	case <-w.pool.quitChan:
		result.Error = fmt.Errorf("worker pool shutdown during processing")
	}
}

// generateQRWithBackground generates QR code with background image with parallel processing and timeout
func (w *worker) generateQRWithBackground(req interfaces.QRRequest, qrBuf, imageBuf *bytes.Buffer) ([]byte, error) {
	// Channel for parallel operations with timeout support
	type result struct {
		data interface{}
		err  error
	}
	
	qrChan := make(chan result, 1)
	bgChan := make(chan result, 1)
	
	// Set timeout for parallel operations (default 3 seconds for each operation)
	operationTimeout := 3 * time.Second
	if w.pool.config.JobTimeout > 0 {
		// Use 60% of job timeout for each parallel operation
		operationTimeout = time.Duration(float64(w.pool.config.JobTimeout) * 0.6)
	}
	
	// Generate QR code in parallel with timeout
	go func() {
		defer func() {
			if r := recover(); r != nil {
				qrChan <- result{err: fmt.Errorf("QR generation panic: %v", r)}
			}
		}()
		
		qrImage, err := w.pool.qrGenerator.Generate(req.Data, req.Width, req.Height)
		qrChan <- result{data: qrImage, err: err}
	}()
	
	// Load background image in parallel with timeout
	go func() {
		defer func() {
			if r := recover(); r != nil {
				bgChan <- result{err: fmt.Errorf("background loading panic: %v", r)}
			}
		}()
		
		bgImage, err := w.pool.storageManager.LoadImage(req.ImagePath)
		bgChan <- result{data: bgImage, err: err}
	}()
	
	// Wait for both operations with timeout
	var qrResult, bgResult result
	var qrDone, bgDone bool
	
	timeoutTimer := time.NewTimer(operationTimeout)
	defer timeoutTimer.Stop()
	
	for !qrDone || !bgDone {
		select {
		case qrResult = <-qrChan:
			qrDone = true
		case bgResult = <-bgChan:
			bgDone = true
		case <-timeoutTimer.C:
			return nil, fmt.Errorf("parallel processing timeout after %v", operationTimeout)
		case <-w.quitChan:
			return nil, fmt.Errorf("worker shutdown during parallel processing")
		case <-w.pool.quitChan:
			return nil, fmt.Errorf("worker pool shutdown during parallel processing")
		}
	}
	
	// Check results from parallel operations
	if qrResult.err != nil {
		return nil, fmt.Errorf("QR generation failed: %w", qrResult.err)
	}
	if bgResult.err != nil {
		return nil, fmt.Errorf("background image loading failed: %w", bgResult.err)
	}
	
	// Validate that we have valid images
	if qrResult.data == nil {
		return nil, fmt.Errorf("QR generation returned nil image")
	}
	if bgResult.data == nil {
		return nil, fmt.Errorf("background image loading returned nil image")
	}
	
	// Composite images with timeout
	compositeDone := make(chan error, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				compositeDone <- fmt.Errorf("image composition panic: %v", r)
			}
		}()
		
		err := w.pool.compositor.CompositeToBuffer(
			bgResult.data.(image.Image),
			qrResult.data.(image.Image),
			req.X, req.Y,
			imageBuf,
			req.OutputFormat,
		)
		compositeDone <- err
	}()
	
	// Wait for composition with timeout
	compositeTimeout := time.NewTimer(2 * time.Second)
	defer compositeTimeout.Stop()
	
	select {
	case err := <-compositeDone:
		if err != nil {
			return nil, fmt.Errorf("image composition failed: %w", err)
		}
	case <-compositeTimeout.C:
		return nil, fmt.Errorf("image composition timeout")
	case <-w.quitChan:
		return nil, fmt.Errorf("worker shutdown during composition")
	case <-w.pool.quitChan:
		return nil, fmt.Errorf("worker pool shutdown during composition")
	}
	
	// Validate result
	if imageBuf.Len() == 0 {
		return nil, fmt.Errorf("image composition produced empty result")
	}
	
	return imageBuf.Bytes(), nil
}

// generateQRWithBackgroundContext generates QR code with background image with context support
func (w *worker) generateQRWithBackgroundContext(ctx context.Context, req interfaces.QRRequest, qrBuf, imageBuf *bytes.Buffer) ([]byte, error) {
	// Channel for parallel operations with context support
	type result struct {
		data interface{}
		err  error
	}
	
	qrChan := make(chan result, 1)
	bgChan := make(chan result, 1)
	
	// Generate QR code in parallel with context
	go func() {
		defer func() {
			if r := recover(); r != nil {
				select {
				case qrChan <- result{err: fmt.Errorf("QR generation panic: %v", r)}:
				case <-ctx.Done():
				}
			}
		}()
		
		// Check context before starting
		select {
		case <-ctx.Done():
			qrChan <- result{err: ctx.Err()}
			return
		default:
		}
		
		qrImage, err := w.pool.qrGenerator.Generate(req.Data, req.Width, req.Height)
		
		// Check context after generation
		select {
		case qrChan <- result{data: qrImage, err: err}:
		case <-ctx.Done():
			// Context cancelled, don't send result
		}
	}()
	
	// Load background image in parallel with context
	go func() {
		defer func() {
			if r := recover(); r != nil {
				select {
				case bgChan <- result{err: fmt.Errorf("background loading panic: %v", r)}:
				case <-ctx.Done():
				}
			}
		}()
		
		// Check context before starting
		select {
		case <-ctx.Done():
			bgChan <- result{err: ctx.Err()}
			return
		default:
		}
		
		bgImage, err := w.pool.storageManager.LoadImage(req.ImagePath)
		
		// Check context after loading
		select {
		case bgChan <- result{data: bgImage, err: err}:
		case <-ctx.Done():
			// Context cancelled, don't send result
		}
	}()
	
	// Wait for both operations with context cancellation
	var qrResult, bgResult result
	var qrDone, bgDone bool
	
	for !qrDone || !bgDone {
		select {
		case qrResult = <-qrChan:
			qrDone = true
		case bgResult = <-bgChan:
			bgDone = true
		case <-ctx.Done():
			return nil, fmt.Errorf("parallel processing cancelled: %w", ctx.Err())
		case <-w.quitChan:
			return nil, fmt.Errorf("worker shutdown during parallel processing")
		case <-w.pool.quitChan:
			return nil, fmt.Errorf("worker pool shutdown during parallel processing")
		}
	}
	
	// Check results from parallel operations
	if qrResult.err != nil {
		return nil, fmt.Errorf("QR generation failed: %w", qrResult.err)
	}
	if bgResult.err != nil {
		return nil, fmt.Errorf("background image loading failed: %w", bgResult.err)
	}
	
	// Validate that we have valid images
	if qrResult.data == nil {
		return nil, fmt.Errorf("QR generation returned nil image")
	}
	if bgResult.data == nil {
		return nil, fmt.Errorf("background image loading returned nil image")
	}
	
	// Check context before composition
	select {
	case <-ctx.Done():
		return nil, fmt.Errorf("composition cancelled: %w", ctx.Err())
	default:
	}
	
	// Composite images with context
	compositeDone := make(chan error, 1)
	go func() {
		defer func() {
			if r := recover(); r != nil {
				select {
				case compositeDone <- fmt.Errorf("image composition panic: %v", r):
				case <-ctx.Done():
				}
			}
		}()
		
		err := w.pool.compositor.CompositeToBuffer(
			bgResult.data.(image.Image),
			qrResult.data.(image.Image),
			req.X, req.Y,
			imageBuf,
			req.OutputFormat,
		)
		
		select {
		case compositeDone <- err:
		case <-ctx.Done():
			// Context cancelled, don't send result
		}
	}()
	
	// Wait for composition with context
	select {
	case err := <-compositeDone:
		if err != nil {
			return nil, fmt.Errorf("image composition failed: %w", err)
		}
	case <-ctx.Done():
		return nil, fmt.Errorf("image composition cancelled: %w", ctx.Err())
	case <-w.quitChan:
		return nil, fmt.Errorf("worker shutdown during composition")
	case <-w.pool.quitChan:
		return nil, fmt.Errorf("worker pool shutdown during composition")
	}
	
	// Validate result
	if imageBuf.Len() == 0 {
		return nil, fmt.Errorf("image composition produced empty result")
	}
	
	return imageBuf.Bytes(), nil
}