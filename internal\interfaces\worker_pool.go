package interfaces

import (
	"bytes"
	"context"
	"time"
)

// QRRequest represents a request to generate a QR code with background
type QRRequest struct {
	Data         string `json:"data"`
	ImagePath    string `json:"image_path"`
	X            int    `json:"x"`
	Y            int    `json:"y"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	OutputFormat string `json:"output_format"`
}

// QRJob represents a job to be processed by the worker pool
type QRJob struct {
	ID         string
	Request    QRRequest
	ResultChan chan<- QRResult
	Timeout    time.Duration
	Context    context.Context
}

// QRResult represents the result of a QR generation job
type QRResult struct {
	JobID     string
	ImageData []byte
	Error     error
	Duration  time.Duration
}

// WorkerPool manages a pool of workers for processing QR generation jobs
type WorkerPool interface {
	// Submit submits a job to the worker pool and returns a channel for the result
	Submit(job QRJob) <-chan QRResult
	
	// Start initializes and starts the worker pool with specified worker count
	Start(workerCount int) error
	
	// Stop gracefully shuts down the worker pool
	Stop() error
	
	// GetStats returns current worker pool statistics
	GetStats() WorkerPoolStats
	
	// IsRunning returns true if the worker pool is currently running
	IsRunning() bool
}

// WorkerPoolStats provides statistics about the worker pool
type WorkerPoolStats struct {
	ActiveWorkers   int
	QueuedJobs      int
	CompletedJobs   int64
	FailedJobs      int64
	AverageLatency  time.Duration
	BufferPoolStats BufferPoolStats
}

// BufferPool manages reusable byte buffers for memory efficiency
type BufferPool interface {
	// GetQRBuffer returns a buffer for QR code generation
	GetQRBuffer() *bytes.Buffer
	
	// GetImageBuffer returns a buffer for image processing
	GetImageBuffer() *bytes.Buffer
	
	// ReturnQRBuffer returns a QR buffer to the pool for reuse
	ReturnQRBuffer(buf *bytes.Buffer)
	
	// ReturnImageBuffer returns an image buffer to the pool for reuse
	ReturnImageBuffer(buf *bytes.Buffer)
	
	// GetStats returns buffer pool statistics
	GetStats() BufferPoolStats
	
	// Cleanup removes unused buffers and frees memory
	Cleanup()
}

// BufferPoolStats provides statistics about buffer pool usage
type BufferPoolStats struct {
	QRBuffersInUse     int
	QRBuffersAvailable int
	ImageBuffersInUse  int
	ImageBuffersAvailable int
	TotalAllocated     int64
	TotalReused        int64
}

// WorkerConfig holds configuration for worker pool
type WorkerConfig struct {
	WorkerCount     int           `yaml:"worker_count"`
	QueueSize       int           `yaml:"queue_size"`
	JobTimeout      time.Duration `yaml:"job_timeout"`
	ShutdownTimeout time.Duration `yaml:"shutdown_timeout"`
	BufferPoolSize  int           `yaml:"buffer_pool_size"`
	MaxBufferSize   int           `yaml:"max_buffer_size"`
}