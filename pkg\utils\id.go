package utils

import (
	"crypto/rand"
	"encoding/hex"
	"hash/fnv"
)

// GenerateUniqueID creates a cryptographically secure unique identifier
func GenerateUniqueID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// HashToFolder maps a unique ID to a folder number (1-1000) using hash-based distribution
func HashToFolder(id string, maxFolders int) int {
	h := fnv.New32a()
	h.Write([]byte(id))
	return int(h.Sum32()%uint32(maxFolders)) + 1
}