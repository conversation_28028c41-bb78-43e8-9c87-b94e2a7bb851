package handlers

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"qr-background-api/internal/config"
	"qr-background-api/internal/storage"
)

// ExampleUploadHandler demonstrates how to use the upload handler
func ExampleUploadHandler() {
	// Create configuration
	cfg := &config.Config{
		Storage: struct {
			LocalPath   string `yaml:"local_path"`
			CloudBucket string `yaml:"cloud_bucket"`
			MaxFileSize int64  `yaml:"max_file_size"`
		}{
			LocalPath:   "./storage",
			MaxFileSize: 10 << 20, // 10MB
		},
	}

	// Create storage manager
	storageManager := storage.NewStorageManager(cfg)

	// Create upload handler
	uploadHandler := NewUploadHandler(storageManager, cfg)

	// Create HTTP server with upload endpoint
	mux := http.NewServeMux()
	mux.Handle("/upload", uploadHandler)

	// Example usage would be:
	// http.ListenAndServe(":8080", mux)

	fmt.Println("Upload handler configured at /upload endpoint")
	// Output: Upload handler configured at /upload endpoint
}

// TestIntegrationUploadHandler tests the upload handler with real storage manager
func TestIntegrationUploadHandler(t *testing.T) {
	// Skip integration test in CI or if storage path is not available
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Create temporary config for testing
	cfg := &config.Config{
		Storage: struct {
			LocalPath   string `yaml:"local_path"`
			CloudBucket string `yaml:"cloud_bucket"`
			MaxFileSize int64  `yaml:"max_file_size"`
		}{
			LocalPath:   "./test_storage",
			MaxFileSize: 10 << 20,
		},
	}

	// Create real storage manager
	storageManager := storage.NewStorageManager(cfg)

	// Create handler
	handler := NewUploadHandler(storageManager, cfg)

	// Create test image data
	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)

	// Create multipart form
	body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	// Create request
	req := httptest.NewRequest(http.MethodPost, "/upload", body)
	req.Header.Set("Content-Type", contentType)

	// Create response recorder
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	// Check response
	if w.Code != http.StatusOK {
		t.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
		t.Logf("Response body: %s", w.Body.String())
	}

	// Note: In a real integration test, you would also:
	// 1. Verify the file was actually saved to disk
	// 2. Clean up the test files
	// 3. Test with different file sizes and formats
}

// BenchmarkUploadHandler benchmarks the upload handler performance
func BenchmarkUploadHandlerExample(b *testing.B) {
	// Create test config
	cfg := &config.Config{
		Storage: struct {
			LocalPath   string `yaml:"local_path"`
			CloudBucket string `yaml:"cloud_bucket"`
			MaxFileSize int64  `yaml:"max_file_size"`
		}{
			MaxFileSize: 10 << 20,
		},
	}

	// Create mock storage
	mockStorage := &MockStorageManager{}
	handler := NewUploadHandler(mockStorage, cfg)

	// Create test data
	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		// Create multipart form
		body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
		if err != nil {
			b.Fatalf("Failed to create multipart form: %v", err)
		}

		// Create request
		req := httptest.NewRequest(http.MethodPost, "/upload", body)
		req.Header.Set("Content-Type", contentType)

		// Create response recorder
		w := httptest.NewRecorder()

		// Execute request
		handler.ServeHTTP(w, req)

		// Verify success
		if w.Code != http.StatusOK {
			b.Errorf("Expected status %d, got %d", http.StatusOK, w.Code)
		}
	}
}

// TestUploadHandlerConcurrency tests concurrent uploads
func TestUploadHandlerConcurrency(t *testing.T) {
	cfg := &config.Config{
		Storage: struct {
			LocalPath   string `yaml:"local_path"`
			CloudBucket string `yaml:"cloud_bucket"`
			MaxFileSize int64  `yaml:"max_file_size"`
		}{
			MaxFileSize: 10 << 20,
		},
	}

	mockStorage := &MockStorageManager{}
	handler := NewUploadHandler(mockStorage, cfg)

	// Number of concurrent requests
	numRequests := 10
	results := make(chan int, numRequests)

	// Create test data
	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)

	// Launch concurrent requests
	for i := 0; i < numRequests; i++ {
		go func(id int) {
			// Create multipart form
			body, contentType, err := createMultipartForm("file", fmt.Sprintf("test%d.jpg", id), "image/jpeg", testContent)
			if err != nil {
				t.Errorf("Failed to create multipart form: %v", err)
				results <- 500
				return
			}

			// Create request
			req := httptest.NewRequest(http.MethodPost, "/upload", body)
			req.Header.Set("Content-Type", contentType)

			// Create response recorder
			w := httptest.NewRecorder()

			// Execute request
			handler.ServeHTTP(w, req)

			// Send result
			results <- w.Code
		}(i)
	}

	// Collect results
	successCount := 0
	for i := 0; i < numRequests; i++ {
		code := <-results
		if code == http.StatusOK {
			successCount++
		}
	}

	// Verify all requests succeeded
	if successCount != numRequests {
		t.Errorf("Expected %d successful requests, got %d", numRequests, successCount)
	}
}