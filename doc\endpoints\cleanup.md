# Cleanup Endpoint

Clean up old images based on age criteria and manage storage space efficiently.

## Endpoint Details

- **URL**: `/cleanup`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Timeout**: 120 seconds

## Request

### Headers
```http
Content-Type: application/json
Accept: application/json
```

### Body Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `batch_size` | integer | No | Number of images to process within current folder (default: from config) |
| `max_age_hours` | integer | No | Age threshold in hours for cleanup (default: from config) |

### Request Example
```json
{
  "batch_size": 50,
  "max_age_hours": 24
}
```

### Default Values
If parameters are not provided, values from configuration are used:
- `batch_size`: From `cleanup.batch_size` config
- `max_age_hours`: From `cleanup.max_age_hours` config

## Response

### Success Response (200 OK)
```json
{
  "success": true,
  "message": "Cleanup completed successfully",
  "processed_folder": 347,
  "images_processed": 25,
  "images_moved_to_cloud": 15,
  "images_deleted": 10,
  "next_folder": 348,
  "processing_time_ms": 1250,
  "processed_images": [
    {
      "image_path": "images/347/abc123def456.jpg",
      "action": "moved_to_cloud",
      "age_hours": 36,
      "file_size": 2048576
    },
    {
      "image_path": "images/347/def456ghi789.png",
      "action": "deleted",
      "age_hours": 48,
      "file_size": 1024000
    }
  ]
}
```

### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `success` | boolean | Always `true` for successful cleanup |
| `message` | string | Success message |
| `processed_folder` | integer | Folder number that was processed |
| `images_processed` | integer | Total number of images processed |
| `images_moved_to_cloud` | integer | Number of images moved to cloud storage |
| `images_deleted` | integer | Number of images deleted |
| `next_folder` | integer | Next folder to be processed |
| `processing_time_ms` | integer | Processing time in milliseconds |
| `processed_images` | array | Details of processed images (optional) |

### Processed Image Info

| Field | Type | Description |
|-------|------|-------------|
| `image_path` | string | Path to the processed image |
| `action` | string | Action taken (`moved_to_cloud`, `deleted`, `skipped`) |
| `age_hours` | integer | Age of the image in hours |
| `file_size` | integer | File size in bytes |

## Error Responses

### 400 Bad Request - Invalid Request
```json
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid request format or missing required fields",
    "details": "invalid JSON format"
  }
}
```

### 400 Bad Request - Invalid Parameters
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": [
      {
        "field": "batch_size",
        "message": "Batch size must be between 1 and 1000",
        "value": 0
      },
      {
        "field": "max_age_hours",
        "message": "Max age must be positive",
        "value": -1
      }
    ]
  }
}
```

### 405 Method Not Allowed
```json
{
  "success": false,
  "error": {
    "code": "METHOD_NOT_ALLOWED",
    "message": "Method not allowed",
    "details": "Only POST method is supported"
  }
}
```

### 500 Internal Server Error - Cleanup Failed
```json
{
  "success": false,
  "error": {
    "code": "CLEANUP_FAILED",
    "message": "Cleanup operation failed",
    "details": "failed to access storage directory"
  }
}
```

### 408 Request Timeout
```json
{
  "success": false,
  "error": {
    "code": "CLEANUP_TIMEOUT",
    "message": "Cleanup operation timeout",
    "details": "Processing exceeded 120 second timeout"
  }
}
```

## Examples

### cURL Example
```bash
curl -X POST http://localhost:8080/cleanup \
  -H "Content-Type: application/json" \
  -d '{
    "batch_size": 50,
    "max_age_hours": 24
  }'
```

### JavaScript Example
```javascript
const cleanupRequest = {
  batch_size: 50,
  max_age_hours: 24
};

fetch('http://localhost:8080/cleanup', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(cleanupRequest)
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log(`Cleanup completed: ${data.images_processed} images processed`);
    console.log(`Moved to cloud: ${data.images_moved_to_cloud}`);
    console.log(`Deleted: ${data.images_deleted}`);
  } else {
    console.error('Cleanup failed:', data.error);
  }
});
```

### Python Example
```python
import requests
import json

url = 'http://localhost:8080/cleanup'
data = {
    'batch_size': 50,
    'max_age_hours': 24
}

response = requests.post(url, json=data)
result = response.json()

if result['success']:
    print(f"Cleanup completed successfully")
    print(f"Images processed: {result['images_processed']}")
    print(f"Images moved to cloud: {result['images_moved_to_cloud']}")
    print(f"Images deleted: {result['images_deleted']}")
    print(f"Processing time: {result['processing_time_ms']}ms")
else:
    print(f"Cleanup failed: {result['error']['message']}")
```

## Cleanup Logic

### Processing Strategy
1. **Folder-Based**: Processes one folder at a time
2. **Sequential**: Folders are processed in numerical order
3. **Resumable**: Tracks last processed folder for continuation
4. **Batch Limited**: Processes limited number of images per folder

### Age-Based Actions
- **Recent Images**: Kept in local storage
- **Older Images**: Moved to cloud storage (if configured)
- **Very Old Images**: Deleted permanently

### Decision Flow
```
For each image:
├── Age < max_age_hours → Skip (keep local)
├── Age >= max_age_hours AND cloud_enabled → Move to cloud
└── Age >= max_age_hours AND !cloud_enabled → Delete
```

## Configuration

### Cleanup Settings
```yaml
cleanup:
  max_folders: 1000      # Maximum folders to track
  batch_size: 100        # Images per folder per run
  max_age_hours: 24      # Age threshold for cleanup
```

### Cloud Storage Settings
```yaml
cloud_storage:
  enabled: true
  bucket: "qr-background-images"
  region: "us-east-1"
```

## State Management

### Cleanup State File
The cleanup system maintains state in `storage/cleanup_state.json`:

```json
{
  "last_processed_folder": 347,
  "last_run_timestamp": "2025-01-15T10:30:00Z",
  "total_folders_processed": 347,
  "total_images_processed": 15420,
  "total_images_moved": 8230,
  "total_images_deleted": 7190
}
```

## Performance Considerations

- **One Folder Per Run**: Prevents long-running operations
- **Batch Processing**: Limits memory usage
- **Metadata Scanning**: Efficient age calculation
- **Concurrent Safety**: Thread-safe state management

## Best Practices

### Scheduling
- Run cleanup regularly (e.g., hourly via cron)
- Monitor processing time and adjust batch size
- Consider off-peak hours for large cleanups

### Monitoring
- Track cleanup metrics over time
- Monitor storage space usage
- Alert on cleanup failures

### Configuration
- Adjust `max_age_hours` based on usage patterns
- Set appropriate `batch_size` for performance
- Enable cloud storage for cost-effective archival
