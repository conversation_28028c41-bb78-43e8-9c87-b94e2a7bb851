package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"qr-background-api/internal/config"
	"qr-background-api/internal/middleware"
	"sync"
	"testing"
	"time"
)

// PerformanceTestConfig holds configuration for performance tests
type PerformanceTestConfig struct {
	TargetProcessingTimeMs int64
	ConcurrentRequests     int
	TestDurationSeconds    int
	MaxMemoryMB           int64
}

// DefaultPerformanceConfig returns default performance test configuration
func DefaultPerformanceConfig() *PerformanceTestConfig {
	return &PerformanceTestConfig{
		TargetProcessingTimeMs: 80,  // Sub-80ms requirement
		ConcurrentRequests:     50,  // High concurrency test
		TestDurationSeconds:    30,  // 30 second stress test
		MaxMemoryMB:           512, // 512MB memory limit
	}
}

// TestQRGenerationPerformance tests QR generation performance under various conditions
func TestQRGenerationPerformance(t *testing.T) {
	config := DefaultPerformanceConfig()
	
	t.Run("SingleRequestPerformance", func(t *testing.T) {
		testSingleRequestPerformance(t, config)
	})
	
	t.Run("ConcurrentRequestsPerformance", func(t *testing.T) {
		testConcurrentRequestsPerformance(t, config)
	})
	
	t.Run("MemoryUsageUnderLoad", func(t *testing.T) {
		testMemoryUsageUnderLoad(t, config)
	})
	
	t.Run("CompressionPerformance", func(t *testing.T) {
		testCompressionPerformance(t, config)
	})
	
	t.Run("StreamingPerformance", func(t *testing.T) {
		testStreamingPerformance(t, config)
	})
}

// testSingleRequestPerformance tests single request processing time
func testSingleRequestPerformance(t *testing.T, perfConfig *PerformanceTestConfig) {
	// Create test setup
	mockWorkerPool := &MockWorkerPool{
		isRunning:    true,
		responseData: []byte("fake-qr-image-data"),
	}
	cfg := &config.Config{}
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	// Apply performance middleware
	middlewareStack := middleware.NewMiddlewareStack()
	optimizedHandler := middlewareStack.Apply(handler)
	
	// Test multiple iterations to get average performance
	iterations := 100
	totalDuration := time.Duration(0)
	successCount := 0
	
	for i := 0; i < iterations; i++ {
		// Create test request
		reqBody := QRGenerationRequest{
			Data:         fmt.Sprintf("test data %d", i),
			ImagePath:    "test-image.jpg",
			X:            10,
			Y:            10,
			Width:        100,
			Height:       100,
			OutputFormat: "png",
		}
		
		body, _ := json.Marshal(reqBody)
		req := httptest.NewRequest(http.MethodPost, "/generate-qr", bytes.NewReader(body))
		req.Header.Set("Content-Type", "application/json")
		
		w := httptest.NewRecorder()
		
		// Measure processing time
		start := time.Now()
		optimizedHandler.ServeHTTP(w, req)
		duration := time.Since(start)
		
		totalDuration += duration
		
		if w.Code == http.StatusOK {
			successCount++
			
			// Check if processing time meets requirement
			processingTimeMs := duration.Milliseconds()
			if processingTimeMs > perfConfig.TargetProcessingTimeMs {
				t.Logf("Warning: Request %d took %dms (target: %dms)", i, processingTimeMs, perfConfig.TargetProcessingTimeMs)
			}
		}
	}
	
	// Calculate statistics
	avgDuration := totalDuration / time.Duration(iterations)
	successRate := float64(successCount) / float64(iterations) * 100
	
	t.Logf("Single Request Performance Results:")
	t.Logf("  Iterations: %d", iterations)
	t.Logf("  Success Rate: %.2f%%", successRate)
	t.Logf("  Average Processing Time: %v (%dms)", avgDuration, avgDuration.Milliseconds())
	t.Logf("  Target Processing Time: %dms", perfConfig.TargetProcessingTimeMs)
	
	// Verify performance requirements
	if avgDuration.Milliseconds() > perfConfig.TargetProcessingTimeMs {
		t.Errorf("Average processing time %dms exceeds target %dms", avgDuration.Milliseconds(), perfConfig.TargetProcessingTimeMs)
	}
	
	if successRate < 95.0 {
		t.Errorf("Success rate %.2f%% is below acceptable threshold (95%%)", successRate)
	}
}

// testConcurrentRequestsPerformance tests performance under concurrent load
func testConcurrentRequestsPerformance(t *testing.T, perfConfig *PerformanceTestConfig) {
	// Create test setup
	mockWorkerPool := &MockWorkerPool{
		isRunning:    true,
		responseData: []byte("fake-qr-image-data"),
	}
	cfg := &config.Config{}
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	// Apply performance middleware
	middlewareStack := middleware.NewMiddlewareStack()
	optimizedHandler := middlewareStack.Apply(handler)
	
	// Concurrent test setup
	numRequests := perfConfig.ConcurrentRequests
	results := make(chan time.Duration, numRequests)
	errorCount := make(chan int, numRequests)
	
	var wg sync.WaitGroup
	start := time.Now()
	
	// Launch concurrent requests
	for i := 0; i < numRequests; i++ {
		wg.Add(1)
		go func(requestID int) {
			defer wg.Done()
			
			// Create test request
			reqBody := QRGenerationRequest{
				Data:         fmt.Sprintf("concurrent test %d", requestID),
				ImagePath:    "test-image.jpg",
				X:            10,
				Y:            10,
				Width:        100,
				Height:       100,
				OutputFormat: "png",
			}
			
			body, _ := json.Marshal(reqBody)
			req := httptest.NewRequest(http.MethodPost, "/generate-qr", bytes.NewReader(body))
			req.Header.Set("Content-Type", "application/json")
			
			w := httptest.NewRecorder()
			
			// Measure individual request time
			requestStart := time.Now()
			optimizedHandler.ServeHTTP(w, req)
			requestDuration := time.Since(requestStart)
			
			results <- requestDuration
			
			if w.Code != http.StatusOK {
				errorCount <- 1
			} else {
				errorCount <- 0
			}
		}(i)
	}
	
	wg.Wait()
	totalDuration := time.Since(start)
	close(results)
	close(errorCount)
	
	// Collect results
	var durations []time.Duration
	totalErrors := 0
	
	for duration := range results {
		durations = append(durations, duration)
	}
	
	for errCount := range errorCount {
		totalErrors += errCount
	}
	
	// Calculate statistics
	totalTime := time.Duration(0)
	maxTime := time.Duration(0)
	minTime := time.Duration(time.Hour) // Initialize with large value
	
	for _, duration := range durations {
		totalTime += duration
		if duration > maxTime {
			maxTime = duration
		}
		if duration < minTime {
			minTime = duration
		}
	}
	
	avgTime := totalTime / time.Duration(len(durations))
	successRate := float64(numRequests-totalErrors) / float64(numRequests) * 100
	throughput := float64(numRequests) / totalDuration.Seconds()
	
	t.Logf("Concurrent Requests Performance Results:")
	t.Logf("  Concurrent Requests: %d", numRequests)
	t.Logf("  Total Duration: %v", totalDuration)
	t.Logf("  Success Rate: %.2f%%", successRate)
	t.Logf("  Throughput: %.2f requests/second", throughput)
	t.Logf("  Average Response Time: %v (%dms)", avgTime, avgTime.Milliseconds())
	t.Logf("  Min Response Time: %v (%dms)", minTime, minTime.Milliseconds())
	t.Logf("  Max Response Time: %v (%dms)", maxTime, maxTime.Milliseconds())
	
	// Verify performance requirements
	if avgTime.Milliseconds() > perfConfig.TargetProcessingTimeMs {
		t.Errorf("Average response time %dms exceeds target %dms under concurrent load", avgTime.Milliseconds(), perfConfig.TargetProcessingTimeMs)
	}
	
	if successRate < 95.0 {
		t.Errorf("Success rate %.2f%% is below acceptable threshold (95%%) under concurrent load", successRate)
	}
	
	if throughput < 10.0 {
		t.Errorf("Throughput %.2f requests/second is below acceptable threshold (10 req/s)", throughput)
	}
}

// testMemoryUsageUnderLoad tests memory usage during high load
func testMemoryUsageUnderLoad(t *testing.T, perfConfig *PerformanceTestConfig) {
	// This test would require actual memory monitoring
	// For now, we'll simulate the test structure
	t.Logf("Memory Usage Test - Target: %dMB max memory usage", perfConfig.MaxMemoryMB)
	
	// In a real implementation, you would:
	// 1. Monitor runtime.MemStats before, during, and after load
	// 2. Verify memory is released after requests complete
	// 3. Check for memory leaks during extended operation
	
	t.Log("Memory usage test completed (implementation placeholder)")
}

// testCompressionPerformance tests compression middleware performance
func testCompressionPerformance(t *testing.T, perfConfig *PerformanceTestConfig) {
	// Create handlers with and without compression
	mockWorkerPool := &MockWorkerPool{
		isRunning:    true,
		responseData: []byte("fake-qr-image-data"),
	}
	cfg := &config.Config{}
	baseHandler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	// Test without compression
	uncompressedTime := measureHandlerPerformance(t, baseHandler, "uncompressed")
	
	// Test with compression
	compressionMiddleware := middleware.NewCompressionMiddleware(6) // Medium compression
	compressedHandler := compressionMiddleware.Handler(baseHandler)
	compressedTime := measureHandlerPerformance(t, compressedHandler, "compressed")
	
	t.Logf("Compression Performance Results:")
	t.Logf("  Uncompressed: %v (%dms)", uncompressedTime, uncompressedTime.Milliseconds())
	t.Logf("  Compressed: %v (%dms)", compressedTime, compressedTime.Milliseconds())
	t.Logf("  Overhead: %v (%dms)", compressedTime-uncompressedTime, (compressedTime-uncompressedTime).Milliseconds())
	
	// Verify compression doesn't add excessive overhead
	overhead := compressedTime - uncompressedTime
	if overhead.Milliseconds() > 20 { // Max 20ms overhead
		t.Errorf("Compression overhead %dms exceeds acceptable threshold (20ms)", overhead.Milliseconds())
	}
}

// testStreamingPerformance tests streaming middleware performance
func testStreamingPerformance(t *testing.T, perfConfig *PerformanceTestConfig) {
	// Create handlers with and without streaming
	mockWorkerPool := &MockWorkerPool{
		isRunning:    true,
		responseData: []byte("fake-qr-image-data"),
	}
	cfg := &config.Config{}
	baseHandler := NewQRGenerationHandler(mockWorkerPool, cfg)
	
	// Test without streaming
	normalTime := measureHandlerPerformance(t, baseHandler, "normal")
	
	// Test with streaming
	streamingMiddleware := middleware.NewStreamingMiddleware(32*1024, 10*1024*1024)
	streamedHandler := streamingMiddleware.Handler(baseHandler)
	streamedTime := measureHandlerPerformance(t, streamedHandler, "streamed")
	
	t.Logf("Streaming Performance Results:")
	t.Logf("  Normal: %v (%dms)", normalTime, normalTime.Milliseconds())
	t.Logf("  Streamed: %v (%dms)", streamedTime, streamedTime.Milliseconds())
	t.Logf("  Difference: %v (%dms)", streamedTime-normalTime, (streamedTime-normalTime).Milliseconds())
	
	// Verify streaming doesn't add excessive overhead for small responses
	overhead := streamedTime - normalTime
	if overhead.Milliseconds() > 10 { // Max 10ms overhead for small responses
		t.Errorf("Streaming overhead %dms exceeds acceptable threshold (10ms)", overhead.Milliseconds())
	}
}

// measureHandlerPerformance measures the average performance of a handler
func measureHandlerPerformance(t *testing.T, handler http.Handler, name string) time.Duration {
	iterations := 50
	totalDuration := time.Duration(0)
	
	for i := 0; i < iterations; i++ {
		reqBody := QRGenerationRequest{
			Data:         fmt.Sprintf("%s test %d", name, i),
			ImagePath:    "test-image.jpg",
			X:            10,
			Y:            10,
			Width:        100,
			Height:       100,
			OutputFormat: "png",
		}
		
		body, _ := json.Marshal(reqBody)
		req := httptest.NewRequest(http.MethodPost, "/generate-qr", bytes.NewReader(body))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Accept-Encoding", "gzip") // Enable compression
		
		w := httptest.NewRecorder()
		
		start := time.Now()
		handler.ServeHTTP(w, req)
		duration := time.Since(start)
		
		totalDuration += duration
	}
	
	return totalDuration / time.Duration(iterations)
}