package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"qr-background-api/internal/config"
	"qr-background-api/internal/errors"
	"qr-background-api/internal/interfaces"
	"qr-background-api/internal/logging"
	"qr-background-api/internal/timeout"
)

// ManagementHandler handles image management requests
type ManagementHandler struct {
	metadataManager interfaces.MetadataManager
	storageManager  interfaces.StorageManager
	config          *config.Config
}

// NewManagementHandler creates a new ManagementHandler instance
func NewManagementHandler(metadataManager interfaces.MetadataManager, storageManager interfaces.StorageManager, cfg *config.Config) *ManagementHandler {
	return &ManagementHandler{
		metadataManager: metadataManager,
		storageManager:  storageManager,
		config:          cfg,
	}
}

// ListImagesRequest represents the list images request structure
type ListImagesRequest struct {
	Page     int `json:"page,omitempty"`      // Page number (1-based)
	PageSize int `json:"page_size,omitempty"` // Number of items per page
}

// ListImagesResponse represents the list images response structure
type ListImagesResponse struct {
	Success    bool           `json:"success"`
	Message    string         `json:"message,omitempty"`
	Images     []ImageInfo    `json:"images"`
	Pagination PaginationInfo `json:"pagination"`
}

// ImageInfo represents image information for listing
type ImageInfo struct {
	ID           string    `json:"id"` // Unique ID extracted from path
	ImagePath    string    `json:"image_path"`
	OriginalName string    `json:"original_name"`
	FileSize     int64     `json:"file_size"`
	ContentType  string    `json:"content_type"`
	CreatedAt    time.Time `json:"created_at"`
	LastAccessed time.Time `json:"last_accessed"`
	Location     string    `json:"location"`
}

// PaginationInfo represents pagination information
type PaginationInfo struct {
	CurrentPage int  `json:"current_page"`
	PageSize    int  `json:"page_size"`
	TotalItems  int  `json:"total_items"`
	TotalPages  int  `json:"total_pages"`
	HasNext     bool `json:"has_next"`
	HasPrevious bool `json:"has_previous"`
}

// DeleteImageResponse represents the delete image response structure
type DeleteImageResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	ImageID string `json:"image_id"`
}

// GetImageResponse represents the get image response structure
type GetImageResponse struct {
	Success   bool      `json:"success"`
	Message   string    `json:"message,omitempty"`
	ImageInfo ImageInfo `json:"image_info"`
}

// Legacy APIError type for backward compatibility

// Error definitions for management operations
var (
	ErrManagementInvalidRequest    = errors.NewAPIError("MANAGEMENT_INVALID_REQUEST", "Invalid management request", 400)
	ErrManagementInvalidPagination = errors.NewAPIError("MANAGEMENT_INVALID_PAGINATION", "Invalid pagination parameters", 400)
	ErrManagementImageNotFound     = errors.NewAPIError("MANAGEMENT_IMAGE_NOT_FOUND", "Image not found", 404)
	ErrManagementInvalidImageID    = errors.NewAPIError("MANAGEMENT_INVALID_IMAGE_ID", "Invalid image ID", 400)
	ErrManagementDeleteFailed      = errors.NewAPIError("MANAGEMENT_DELETE_FAILED", "Image deletion failed", 500)
	ErrManagementListFailed        = errors.NewAPIError("MANAGEMENT_LIST_FAILED", "Image listing failed", 500)
)

// ServeHTTP implements the http.Handler interface for management endpoints
func (h *ManagementHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(r.Context(), timeout.GetManager().GetConfig().HTTPRequest)
	defer cancel()
	r = r.WithContext(ctx)

	// Get logger with request context
	logger := logging.GetLogger().WithContext(ctx).WithFields(map[string]interface{}{
		"handler": "management",
		"method":  r.Method,
		"path":    r.URL.Path,
	})

	logger.Info("Processing management request")

	// Set response headers
	w.Header().Set("Content-Type", "application/json")
	SetCORSHeaders(w)

	// Handle preflight requests
	if HandlePreflight(w, r) {
		return
	}

	// Route based on path and method
	path := strings.TrimPrefix(r.URL.Path, "/images")

	switch {
	case path == "" && r.Method == http.MethodGet:
		// GET /images - List images
		h.handleListImages(w, r)
	case strings.HasPrefix(path, "/") && r.Method == http.MethodGet:
		// GET /images/{id} - Get image info
		imageID := strings.TrimPrefix(path, "/")
		h.handleGetImage(w, r, imageID)
	case strings.HasPrefix(path, "/") && r.Method == http.MethodDelete:
		// DELETE /images/{id} - Delete image
		imageID := strings.TrimPrefix(path, "/")
		h.handleDeleteImage(w, r, imageID)
	default:
		logger.Warn("Method not allowed")
		apiErr := errors.NewAPIError("METHOD_NOT_ALLOWED", "Method not allowed", 405).WithDetails("Supported methods: GET, DELETE")
		errors.WriteErrorResponse(w, r, apiErr)
	}
}

// handleListImages handles GET /images endpoint
func (h *ManagementHandler) handleListImages(w http.ResponseWriter, r *http.Request) {
	// Parse query parameters
	page := 1
	pageSize := 20 // Default page size

	if pageStr := r.URL.Query().Get("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		} else {
			h.writeErrorResponse(w, http.StatusBadRequest, ErrManagementInvalidPagination)
			return
		}
	}

	if pageSizeStr := r.URL.Query().Get("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		} else {
			h.writeErrorResponse(w, http.StatusBadRequest, ErrManagementInvalidPagination)
			return
		}
	}

	// Get images from all folders
	allImages, err := h.getAllImages()
	if err != nil {
		h.writeErrorResponse(w, http.StatusInternalServerError, ErrManagementListFailed)
		return
	}

	// Calculate pagination
	totalItems := len(allImages)
	totalPages := (totalItems + pageSize - 1) / pageSize
	if totalPages == 0 {
		totalPages = 1
	}

	// Get items for current page
	startIdx := (page - 1) * pageSize
	endIdx := startIdx + pageSize
	if endIdx > totalItems {
		endIdx = totalItems
	}

	var pageImages []ImageInfo
	if startIdx < totalItems {
		pageImages = allImages[startIdx:endIdx]
	} else {
		pageImages = []ImageInfo{}
	}

	// Create response
	response := ListImagesResponse{
		Success: true,
		Message: "Images retrieved successfully",
		Images:  pageImages,
		Pagination: PaginationInfo{
			CurrentPage: page,
			PageSize:    pageSize,
			TotalItems:  totalItems,
			TotalPages:  totalPages,
			HasNext:     page < totalPages,
			HasPrevious: page > 1,
		},
	}

	if err := WriteJSONResponse(w, http.StatusOK, response); err != nil {
		fmt.Printf("Failed to write list images response: %v\n", err)
	}
}

// handleGetImage handles GET /images/{id} endpoint
func (h *ManagementHandler) handleGetImage(w http.ResponseWriter, r *http.Request, imageID string) {
	// Validate image ID
	if imageID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, ErrManagementInvalidImageID)
		return
	}

	// Find image by ID
	imageInfo, err := h.getImageByID(imageID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			h.writeErrorResponse(w, http.StatusNotFound, ErrManagementImageNotFound)
		} else {
			h.writeErrorResponse(w, http.StatusInternalServerError, ErrManagementListFailed)
		}
		return
	}

	// Update last access time
	if err := h.metadataManager.UpdateLastAccess(imageInfo.ImagePath); err != nil {
		// Log error but don't fail the request
		fmt.Printf("Failed to update last access for %s: %v\n", imageInfo.ImagePath, err)
	}

	response := GetImageResponse{
		Success:   true,
		Message:   "Image information retrieved successfully",
		ImageInfo: *imageInfo,
	}

	if err := WriteJSONResponse(w, http.StatusOK, response); err != nil {
		fmt.Printf("Failed to write get image response: %v\n", err)
	}
}

// handleDeleteImage handles DELETE /images/{id} endpoint
func (h *ManagementHandler) handleDeleteImage(w http.ResponseWriter, r *http.Request, imageID string) {
	// Validate image ID
	if imageID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, ErrManagementInvalidImageID)
		return
	}

	// Find image by ID
	imageInfo, err := h.getImageByID(imageID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			h.writeErrorResponse(w, http.StatusNotFound, ErrManagementImageNotFound)
		} else {
			h.writeErrorResponse(w, http.StatusInternalServerError, ErrManagementListFailed)
		}
		return
	}

	// Delete image from storage
	if err := h.storageManager.DeleteImage(imageInfo.ImagePath); err != nil {
		h.writeErrorResponse(w, http.StatusInternalServerError, ErrManagementDeleteFailed)
		return
	}

	// Delete metadata
	if err := h.metadataManager.DeleteImageMetadata(imageInfo.ImagePath); err != nil {
		// Log error but continue since image is already deleted
		fmt.Printf("Failed to delete metadata for %s: %v\n", imageInfo.ImagePath, err)
	}

	response := DeleteImageResponse{
		Success: true,
		Message: MsgDeleteSuccess,
		ImageID: imageID,
	}

	if err := WriteJSONResponse(w, http.StatusOK, response); err != nil {
		fmt.Printf("Failed to write delete image response: %v\n", err)
	}
}

// getAllImages retrieves all images from all folders
func (h *ManagementHandler) getAllImages() ([]ImageInfo, error) {
	var allImages []ImageInfo

	// Iterate through all folders (1-1000)
	for folderNum := 1; folderNum <= h.config.Cleanup.MaxFolders; folderNum++ {
		// Get all images from this folder (no age limit, large limit)
		images, err := h.metadataManager.GetImagesForCleanup(folderNum, 0, 10000)
		if err != nil {
			// Log error but continue with other folders
			fmt.Printf("Failed to get images from folder %d: %v\n", folderNum, err)
			continue
		}

		// Convert to ImageInfo
		for _, img := range images {
			imageInfo := ImageInfo{
				ID:           h.extractIDFromPath(img.ImagePath),
				ImagePath:    img.ImagePath,
				OriginalName: img.OriginalName,
				FileSize:     img.FileSize,
				ContentType:  img.ContentType,
				CreatedAt:    img.CreatedAt,
				LastAccessed: img.LastAccessed,
				Location:     img.Location,
			}
			allImages = append(allImages, imageInfo)
		}
	}

	return allImages, nil
}

// getImageByID finds an image by its unique ID
func (h *ManagementHandler) getImageByID(imageID string) (*ImageInfo, error) {
	// Determine folder from ID
	folderNum := h.storageManager.GetFolderFromID(imageID)

	// Get all images from the folder
	images, err := h.metadataManager.GetImagesForCleanup(folderNum, 0, 10000)
	if err != nil {
		return nil, fmt.Errorf("failed to get images from folder %d: %w", folderNum, err)
	}

	// Find image with matching ID
	for _, img := range images {
		if h.extractIDFromPath(img.ImagePath) == imageID {
			imageInfo := &ImageInfo{
				ID:           imageID,
				ImagePath:    img.ImagePath,
				OriginalName: img.OriginalName,
				FileSize:     img.FileSize,
				ContentType:  img.ContentType,
				CreatedAt:    img.CreatedAt,
				LastAccessed: img.LastAccessed,
				Location:     img.Location,
			}
			return imageInfo, nil
		}
	}

	return nil, fmt.Errorf("image with ID %s not found", imageID)
}

// extractIDFromPath extracts the unique ID from an image path
func (h *ManagementHandler) extractIDFromPath(imagePath string) string {
	// Path format: images/123/abc123def456.jpg
	// Extract filename without extension
	filename := filepath.Base(imagePath)
	ext := filepath.Ext(filename)
	return strings.TrimSuffix(filename, ext)
}

// writeErrorResponse writes an error response
func (h *ManagementHandler) writeErrorResponse(w http.ResponseWriter, statusCode int, apiErr *errors.APIError) {
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(apiErr)
}
