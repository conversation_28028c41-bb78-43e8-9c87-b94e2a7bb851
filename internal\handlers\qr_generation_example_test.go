package handlers

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"

	"qr-background-api/internal/config"
)

// ExampleQRGenerationHand<PERSON> demonstrates how to use the QR generation handler
func ExampleQRGenerationHandler() {
	// Create configuration
	cfg := &config.Config{
		WorkerPool: struct {
			Size           int `yaml:"size"`
			QueueSize      int `yaml:"queue_size"`
			BufferPoolSize int `yaml:"buffer_pool_size"`
		}{
			Size:           4,
			QueueSize:      100,
			BufferPoolSize: 10,
		},
	}

	// Create mock worker pool for demonstration
	mockWorkerPool := &MockWorkerPool{
		isRunning:    true,
		responseData: []byte("generated-qr-image-data"),
	}

	// Create QR generation handler
	qrHandler := NewQRGenerationHandler(mockWorkerPool, cfg)

	// Create HTTP server with QR generation endpoint
	mux := http.NewServeMux()
	mux.Handle("/generate-qr", qrHandler)

	// Example usage would be:
	// http.ListenAndServe(":8080", mux)

	fmt.Println("QR generation handler configured at /generate-qr endpoint")
	// Output: QR generation handler configured at /generate-qr endpoint
}

// ExampleQRGenerationHandler_request demonstrates a sample QR generation request
func ExampleQRGenerationHandler_request() {
	// Create configuration and mock worker pool
	cfg := &config.Config{}
	mockWorkerPool := &MockWorkerPool{
		isRunning:    true,
		responseData: []byte("qr-image-data"),
	}

	// Create handler
	handler := NewQRGenerationHandler(mockWorkerPool, cfg)

	// Sample request JSON
	requestJSON := `{
		"data": "https://example.com",
		"image_path": "images/1/background.jpg",
		"x": 50,
		"y": 50,
		"width": 200,
		"height": 200,
		"output_format": "png"
	}`

	// Create test request
	req := httptest.NewRequest(http.MethodPost, "/generate-qr", strings.NewReader(requestJSON))
	req.Header.Set("Content-Type", "application/json")

	// Create test response recorder
	w := httptest.NewRecorder()

	// Handle the request
	handler.ServeHTTP(w, req)

	// Check response
	if w.Code == http.StatusOK {
		fmt.Printf("QR generation successful, Content-Type: %s\n", w.Header().Get("Content-Type"))
	} else {
		fmt.Printf("QR generation failed with status: %d\n", w.Code)
	}

	// Output: QR generation successful, Content-Type: image/png
}

// ExampleQRGenerationHandler_validation demonstrates request validation
func ExampleQRGenerationHandler_validation() {
	// Create handler
	handler := &QRGenerationHandler{}

	// Test various validation scenarios
	validRequest := QRGenerationRequest{
		Data:         "Hello, World!",
		ImagePath:    "images/1/background.jpg",
		X:            10,
		Y:            20,
		Width:        150,
		Height:       150,
		OutputFormat: "jpeg",
	}

	// Validate the request
	err := handler.validateRequest(&validRequest)
	if err == nil {
		fmt.Println("Request validation passed")
	} else {
		fmt.Printf("Validation error: %s\n", err.Message)
	}

	// Test invalid request
	invalidRequest := QRGenerationRequest{
		// Missing required data field
		ImagePath: "images/1/background.jpg",
		Width:     100,
		Height:    100,
	}

	err = handler.validateRequest(&invalidRequest)
	if err != nil {
		fmt.Printf("Expected validation error: %s\n", err.Message)
	}

	// Output:
	// Request validation passed
	// Expected validation error: QR data is required
}