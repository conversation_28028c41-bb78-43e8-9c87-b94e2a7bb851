package server

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"sync"
	"testing"
	"time"

	"qr-background-api/internal/config"
	"qr-background-api/internal/logging"
	"qr-background-api/internal/storage"
)

// TestServerIntegration tests the complete HTTP server functionality
func TestServerIntegration(t *testing.T) {
	// Initialize logger for tests
	loggerConfig := &logging.Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}
	logging.InitGlobalLogger(loggerConfig)

	// Create test configuration
	cfg := createTestConfig(t)
	defer cleanupTestStorage(cfg.Storage.LocalPath)

	// Create storage components
	storageManager := storage.NewStorageManager(cfg)
	metadataManager := storage.NewFilesystemMetadataManager(cfg.Storage.LocalPath)

	// Create server
	server, err := New(cfg, storageManager, metadataManager)
	if err != nil {
		t.Fatalf("Failed to create server: %v", err)
	}

	// Start worker pool for QR generation tests
	if err := server.workerPool.Start(cfg.WorkerPool.Size); err != nil {
		t.Fatalf("Failed to start worker pool: %v", err)
	}
	defer server.workerPool.Stop()

	// Start server in background
	testServer := httptest.NewServer(server.server.Handler)
	defer testServer.Close()

	// Run integration tests
	t.Run("HealthCheck", func(t *testing.T) {
		testHealthCheck(t, testServer.URL)
	})

	t.Run("UploadFlow", func(t *testing.T) {
		testUploadFlow(t, testServer.URL)
	})

	t.Run("QRGenerationFlow", func(t *testing.T) {
		testQRGenerationFlow(t, testServer.URL)
	})

	t.Run("ManagementFlow", func(t *testing.T) {
		testManagementFlow(t, testServer.URL)
	})

	t.Run("CleanupFlow", func(t *testing.T) {
		testCleanupFlow(t, testServer.URL)
	})

	t.Run("MiddlewareIntegration", func(t *testing.T) {
		testMiddlewareIntegration(t, testServer.URL)
	})

	t.Run("ErrorHandling", func(t *testing.T) {
		testErrorHandling(t, testServer.URL)
	})

	t.Run("ConcurrentRequests", func(t *testing.T) {
		testConcurrentRequests(t, testServer.URL)
	})

	t.Run("PerformanceRequirements", func(t *testing.T) {
		testPerformanceRequirements(t, testServer.URL)
	})
}

// TestGracefulShutdown tests the graceful shutdown functionality
func TestGracefulShutdown(t *testing.T) {
	// Create test configuration
	cfg := createTestConfig(t)
	defer cleanupTestStorage(cfg.Storage.LocalPath)

	// Create storage components
	storageManager := storage.NewStorageManager(cfg)
	metadataManager := storage.NewFilesystemMetadataManager(cfg.Storage.LocalPath)

	// Create server
	server, err := New(cfg, storageManager, metadataManager)
	if err != nil {
		t.Fatalf("Failed to create server: %v", err)
	}

	// Start server in background
	serverStarted := make(chan struct{})
	serverStopped := make(chan error)

	go func() {
		close(serverStarted)
		err := server.Start()
		serverStopped <- err
	}()

	// Wait for server to start
	<-serverStarted
	time.Sleep(100 * time.Millisecond) // Give server time to bind to port

	// Test graceful shutdown
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	shutdownErr := server.Stop(shutdownCtx)
	if shutdownErr != nil {
		t.Errorf("Graceful shutdown failed: %v", shutdownErr)
	}

	// Verify server stopped
	select {
	case err := <-serverStopped:
		if err != nil && err != http.ErrServerClosed {
			t.Errorf("Server stopped with unexpected error: %v", err)
		}
	case <-time.After(6 * time.Second):
		t.Error("Server did not stop within timeout")
	}
}

// createTestConfig creates a test configuration
func createTestConfig(t *testing.T) *config.Config {
	tempDir := t.TempDir()

	return &config.Config{
		Server: struct {
			Port              int  `yaml:"port"`
			ReadTimeout       int  `yaml:"read_timeout"`
			WriteTimeout      int  `yaml:"write_timeout"`
			EnableCompression bool `yaml:"enable_compression"`
			CompressionLevel  int  `yaml:"compression_level"`
		}{
			Port:              0, // Use random port for testing
			ReadTimeout:       30,
			WriteTimeout:      30,
			EnableCompression: true,
			CompressionLevel:  6,
		},
		WorkerPool: struct {
			Size           int `yaml:"size"`
			QueueSize      int `yaml:"queue_size"`
			BufferPoolSize int `yaml:"buffer_pool_size"`
		}{
			Size:           2,
			QueueSize:      10,
			BufferPoolSize: 5,
		},
		Storage: struct {
			LocalPath   string `yaml:"local_path"`
			CloudBucket string `yaml:"cloud_bucket"`
			MaxFileSize int64  `yaml:"max_file_size"`
		}{
			LocalPath:   tempDir,
			MaxFileSize: 10 << 20, // 10MB
		},
		Performance: struct {
			EnableCompression bool  `yaml:"enable_compression"`
			CompressionLevel  int   `yaml:"compression_level"`
			EnableStreaming   bool  `yaml:"enable_streaming"`
			StreamBufferSize  int   `yaml:"stream_buffer_size"`
			MaxStreamMemory   int64 `yaml:"max_stream_memory"`
			EnableMemoryOpt   bool  `yaml:"enable_memory_optimization"`
			MemoryLimit       int64 `yaml:"memory_limit"`
			GCInterval        int   `yaml:"gc_interval_seconds"`
			ForceGCThreshold  int64 `yaml:"force_gc_threshold"`
		}{
			EnableCompression: true,
			CompressionLevel:  6,
			EnableStreaming:   true,
			StreamBufferSize:  32 * 1024,
			MaxStreamMemory:   10 * 1024 * 1024,
			EnableMemoryOpt:   true,
			MemoryLimit:       512 * 1024 * 1024,
			GCInterval:        30,
			ForceGCThreshold:  256 * 1024 * 1024,
		},
	}
}

// cleanupTestStorage removes test storage directory
func cleanupTestStorage(path string) {
	os.RemoveAll(path)
}

// testHealthCheck tests the health check endpoint
func testHealthCheck(t *testing.T, baseURL string) {
	resp, err := http.Get(baseURL + "/health")
	if err != nil {
		t.Fatalf("Health check request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}

	if resp.Header.Get("Content-Type") != "application/json" {
		t.Errorf("Expected Content-Type application/json, got %s", resp.Header.Get("Content-Type"))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Failed to read response body: %v", err)
	}

	var healthResp map[string]interface{}
	if err := json.Unmarshal(body, &healthResp); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	if healthResp["status"] != "healthy" {
		t.Errorf("Expected status 'healthy', got %v", healthResp["status"])
	}

	if healthResp["service"] != "qr-background-api" {
		t.Errorf("Expected service 'qr-background-api', got %v", healthResp["service"])
	}
}

// testUploadFlow tests the complete upload flow
func testUploadFlow(t *testing.T, baseURL string) {
	// Create test image data
	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)

	// Create multipart form
	body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	// Make upload request
	resp, err := http.Post(baseURL+"/upload", contentType, body)
	if err != nil {
		t.Fatalf("Upload request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		t.Fatalf("Expected status 200, got %d. Body: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Failed to read response body: %v", err)
	}

	var uploadResp map[string]interface{}
	if err := json.Unmarshal(responseBody, &uploadResp); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	if uploadResp["success"] != true {
		t.Errorf("Expected success true, got %v", uploadResp["success"])
	}

	if uploadResp["image_path"] == nil || uploadResp["image_path"] == "" {
		t.Error("Expected non-empty image_path in response")
	}
}

// testQRGenerationFlow tests the QR generation flow
func testQRGenerationFlow(t *testing.T, baseURL string) {
	// First upload an image to use as background
	imagePath := uploadTestImage(t, baseURL)

	// Create QR generation request
	qrRequest := map[string]interface{}{
		"data":          "https://example.com",
		"image_path":    imagePath,
		"x":             10,
		"y":             20,
		"width":         100,
		"height":        100,
		"output_format": "png",
	}

	requestBody, err := json.Marshal(qrRequest)
	if err != nil {
		t.Fatalf("Failed to marshal QR request: %v", err)
	}

	// Make QR generation request
	resp, err := http.Post(baseURL+"/generate-qr", "application/json", bytes.NewReader(requestBody))
	if err != nil {
		t.Fatalf("QR generation request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		t.Fatalf("Expected status 200, got %d. Body: %s", resp.StatusCode, string(bodyBytes))
	}

	// Verify response is image data
	contentType := resp.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "image/") {
		t.Errorf("Expected image content type, got %s", contentType)
	}

	// Read and verify image data
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Failed to read image data: %v", err)
	}

	if len(imageData) == 0 {
		t.Error("Expected non-empty image data")
	}
}

// testManagementFlow tests the management endpoints
func testManagementFlow(t *testing.T, baseURL string) {
	// Upload test images first
	imagePath1 := uploadTestImage(t, baseURL)
	_ = uploadTestImage(t, baseURL) // Upload second image for list test

	// Test list images
	resp, err := http.Get(baseURL + "/management/images?page=1&page_size=10")
	if err != nil {
		t.Fatalf("List images request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		t.Fatalf("Expected status 200, got %d. Body: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse list response
	listBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Failed to read list response: %v", err)
	}

	var listResp map[string]interface{}
	if err := json.Unmarshal(listBody, &listResp); err != nil {
		t.Fatalf("Failed to parse list response: %v", err)
	}

	if listResp["success"] != true {
		t.Errorf("Expected success true, got %v", listResp["success"])
	}

	// Extract image ID from path for individual image test
	imageID := extractImageIDFromPath(imagePath1)
	if imageID == "" {
		t.Skip("Could not extract image ID from path")
		return
	}

	// Test get individual image
	resp2, err := http.Get(baseURL + "/management/images/" + imageID)
	if err != nil {
		t.Fatalf("Get image request failed: %v", err)
	}
	defer resp2.Body.Close()

	if resp2.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp2.Body)
		t.Fatalf("Expected status 200, got %d. Body: %s", resp2.StatusCode, string(bodyBytes))
	}

	// Test delete image
	req, err := http.NewRequest("DELETE", baseURL+"/management/images/"+imageID, nil)
	if err != nil {
		t.Fatalf("Failed to create delete request: %v", err)
	}

	client := &http.Client{}
	resp3, err := client.Do(req)
	if err != nil {
		t.Fatalf("Delete image request failed: %v", err)
	}
	defer resp3.Body.Close()

	if resp3.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp3.Body)
		t.Fatalf("Expected status 200, got %d. Body: %s", resp3.StatusCode, string(bodyBytes))
	}
}

// testCleanupFlow tests the cleanup endpoint
func testCleanupFlow(t *testing.T, baseURL string) {
	// Upload some test images first
	uploadTestImage(t, baseURL)
	uploadTestImage(t, baseURL)

	// Create cleanup request
	cleanupRequest := map[string]interface{}{
		"batch_size": 10,
		"max_age":    1, // 1 hour
	}

	requestBody, err := json.Marshal(cleanupRequest)
	if err != nil {
		t.Fatalf("Failed to marshal cleanup request: %v", err)
	}

	// Make cleanup request
	resp, err := http.Post(baseURL+"/cleanup", "application/json", bytes.NewReader(requestBody))
	if err != nil {
		t.Fatalf("Cleanup request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		t.Fatalf("Expected status 200, got %d. Body: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Failed to read response body: %v", err)
	}

	var cleanupResp map[string]interface{}
	if err := json.Unmarshal(responseBody, &cleanupResp); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	if cleanupResp["success"] != true {
		t.Errorf("Expected success true, got %v", cleanupResp["success"])
	}
}

// testMiddlewareIntegration tests that all middleware is working together
func testMiddlewareIntegration(t *testing.T, baseURL string) {
	// Test compression middleware by checking headers
	resp, err := http.Get(baseURL + "/health")
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}
	defer resp.Body.Close()

	// Check for request ID header (from logging middleware)
	if resp.Header.Get("X-Request-ID") == "" {
		t.Error("Expected X-Request-ID header from logging middleware")
	}

	// Test timeout middleware with a request that should complete quickly
	start := time.Now()
	resp2, err := http.Get(baseURL + "/health")
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}
	defer resp2.Body.Close()
	duration := time.Since(start)

	if duration > 5*time.Second {
		t.Errorf("Request took too long: %v", duration)
	}

	// Test recovery middleware by making a request that might cause issues
	// (The handlers should be robust, but middleware should catch any panics)
	resp3, err := http.Post(baseURL+"/generate-qr", "application/json", strings.NewReader("invalid json"))
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}
	defer resp3.Body.Close()

	// Should get a proper error response, not a panic
	if resp3.StatusCode == 0 {
		t.Error("Expected proper error response, got connection issue (possible panic)")
	}
}

// testErrorHandling tests various error scenarios
func testErrorHandling(t *testing.T, baseURL string) {
	// Test invalid method on health endpoint
	resp, err := http.Post(baseURL+"/health", "application/json", strings.NewReader("{}"))
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusMethodNotAllowed {
		t.Errorf("Expected status 405, got %d", resp.StatusCode)
	}

	// Test invalid JSON on QR generation
	resp2, err := http.Post(baseURL+"/generate-qr", "application/json", strings.NewReader("invalid json"))
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}
	defer resp2.Body.Close()

	if resp2.StatusCode != http.StatusBadRequest {
		t.Errorf("Expected status 400, got %d", resp2.StatusCode)
	}

	// Test missing file on upload
	resp3, err := http.Post(baseURL+"/upload", "multipart/form-data", strings.NewReader(""))
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}
	defer resp3.Body.Close()

	if resp3.StatusCode == http.StatusOK {
		t.Error("Expected error status for empty upload, got 200")
	}

	// Test non-existent image in management
	resp4, err := http.Get(baseURL + "/management/images/nonexistent")
	if err != nil {
		t.Fatalf("Request failed: %v", err)
	}
	defer resp4.Body.Close()

	if resp4.StatusCode != http.StatusNotFound {
		t.Errorf("Expected status 404, got %d", resp4.StatusCode)
	}
}

// testConcurrentRequests tests the server under concurrent load
func testConcurrentRequests(t *testing.T, baseURL string) {
	const numRequests = 10
	var wg sync.WaitGroup
	results := make(chan int, numRequests)

	// Launch concurrent health check requests
	for i := 0; i < numRequests; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			resp, err := http.Get(baseURL + "/health")
			if err != nil {
				results <- 0
				return
			}
			defer resp.Body.Close()
			results <- resp.StatusCode
		}()
	}

	wg.Wait()
	close(results)

	// Check all requests succeeded
	successCount := 0
	for statusCode := range results {
		if statusCode == http.StatusOK {
			successCount++
		}
	}

	if successCount != numRequests {
		t.Errorf("Expected %d successful requests, got %d", numRequests, successCount)
	}
}

// testPerformanceRequirements tests that the server meets performance requirements
func testPerformanceRequirements(t *testing.T, baseURL string) {
	// Test sub-80ms processing time for QR generation (Requirement 3.4)
	imagePath := uploadTestImage(t, baseURL)

	// Create QR generation request
	qrRequest := map[string]interface{}{
		"data":          "https://example.com",
		"image_path":    imagePath,
		"x":             10,
		"y":             20,
		"width":         100,
		"height":        100,
		"output_format": "png",
	}

	requestBody, err := json.Marshal(qrRequest)
	if err != nil {
		t.Fatalf("Failed to marshal QR request: %v", err)
	}

	// Measure processing time
	start := time.Now()
	resp, err := http.Post(baseURL+"/generate-qr", "application/json", bytes.NewReader(requestBody))
	if err != nil {
		t.Fatalf("QR generation request failed: %v", err)
	}
	defer resp.Body.Close()
	duration := time.Since(start)

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		t.Fatalf("Expected status 200, got %d. Body: %s", resp.StatusCode, string(bodyBytes))
	}

	// Check performance requirement (sub-80ms)
	if duration > 80*time.Millisecond {
		t.Logf("Warning: QR generation took %v, exceeds 80ms target", duration)
		// Don't fail the test as this might be due to test environment
		// but log the warning for visibility
	} else {
		t.Logf("QR generation completed in %v (meets <80ms requirement)", duration)
	}

	// Test multiple concurrent requests for performance
	const numConcurrent = 5
	var wg sync.WaitGroup
	durations := make(chan time.Duration, numConcurrent)

	for i := 0; i < numConcurrent; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			start := time.Now()
			resp, err := http.Post(baseURL+"/generate-qr", "application/json", bytes.NewReader(requestBody))
			if err != nil {
				durations <- time.Hour // Error case
				return
			}
			defer resp.Body.Close()
			durations <- time.Since(start)
		}()
	}

	wg.Wait()
	close(durations)

	// Check concurrent performance
	var totalDuration time.Duration
	successCount := 0
	for d := range durations {
		if d < time.Hour { // Not an error
			totalDuration += d
			successCount++
		}
	}

	if successCount > 0 {
		avgDuration := totalDuration / time.Duration(successCount)
		t.Logf("Average concurrent QR generation time: %v", avgDuration)
	}
}

// Helper functions

// uploadTestImage uploads a test image and returns the image path
func uploadTestImage(t *testing.T, baseURL string) string {
	// Create test image data
	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0}
	testContent := append(jpegHeader, make([]byte, 1000)...)

	// Create multipart form
	body, contentType, err := createMultipartForm("file", "test.jpg", "image/jpeg", testContent)
	if err != nil {
		t.Fatalf("Failed to create multipart form: %v", err)
	}

	// Make upload request
	resp, err := http.Post(baseURL+"/upload", contentType, body)
	if err != nil {
		t.Fatalf("Upload request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		t.Fatalf("Expected status 200, got %d. Body: %s", resp.StatusCode, string(bodyBytes))
	}

	// Parse response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatalf("Failed to read response body: %v", err)
	}

	var uploadResp map[string]interface{}
	if err := json.Unmarshal(responseBody, &uploadResp); err != nil {
		t.Fatalf("Failed to parse JSON response: %v", err)
	}

	imagePath, ok := uploadResp["image_path"].(string)
	if !ok || imagePath == "" {
		t.Fatalf("Expected non-empty image_path in response, got %v", uploadResp["image_path"])
	}

	return imagePath
}

// createMultipartForm creates a multipart form for file upload
func createMultipartForm(fieldName, fileName, contentType string, content []byte) (io.Reader, string, error) {
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// Create form file with proper headers
	part, err := writer.CreatePart(map[string][]string{
		"Content-Disposition": {fmt.Sprintf(`form-data; name="%s"; filename="%s"`, fieldName, fileName)},
		"Content-Type":        {contentType},
	})
	if err != nil {
		return nil, "", err
	}

	// Write content
	if _, err := part.Write(content); err != nil {
		return nil, "", err
	}

	// Close writer
	if err := writer.Close(); err != nil {
		return nil, "", err
	}

	return &buf, writer.FormDataContentType(), nil
}

// extractImageIDFromPath extracts the image ID from an image path
func extractImageIDFromPath(imagePath string) string {
	// Expected format: "images/123/abc123def456.jpg"
	parts := strings.Split(imagePath, "/")
	if len(parts) < 3 {
		return ""
	}

	// Get filename and remove extension
	filename := parts[len(parts)-1]
	if idx := strings.LastIndex(filename, "."); idx > 0 {
		return filename[:idx]
	}

	return filename
}

// TestServerCoreIntegration tests the core server functionality without image processing
func TestServerCoreIntegration(t *testing.T) {
	// Initialize logger for tests
	loggerConfig := &logging.Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		Timestamp:  true,
		Caller:     false,
		StackTrace: false,
	}
	logging.InitGlobalLogger(loggerConfig)

	// Create test configuration
	cfg := createTestConfig(t)
	defer cleanupTestStorage(cfg.Storage.LocalPath)

	// Create storage components
	storageManager := storage.NewStorageManager(cfg)
	metadataManager := storage.NewFilesystemMetadataManager(cfg.Storage.LocalPath)

	// Create server
	server, err := New(cfg, storageManager, metadataManager)
	if err != nil {
		t.Fatalf("Failed to create server: %v", err)
	}

	// Start worker pool for QR generation tests
	if err := server.workerPool.Start(cfg.WorkerPool.Size); err != nil {
		t.Fatalf("Failed to start worker pool: %v", err)
	}
	defer server.workerPool.Stop()

	// Start server in background
	testServer := httptest.NewServer(server.server.Handler)
	defer testServer.Close()

	t.Run("ServerStartup", func(t *testing.T) {
		// Test that server started correctly
		if server.server == nil {
			t.Error("HTTP server should be initialized")
		}

		if !server.workerPool.IsRunning() {
			t.Error("Worker pool should be running")
		}
	})

	t.Run("HealthEndpoint", func(t *testing.T) {
		resp, err := http.Get(testServer.URL + "/health")
		if err != nil {
			t.Fatalf("Health check failed: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}

		// Check middleware headers
		if resp.Header.Get("X-Request-ID") == "" {
			t.Error("Expected X-Request-ID header from middleware")
		}
	})

	t.Run("RoutingAndMiddleware", func(t *testing.T) {
		// Test all endpoints are accessible (even if they return errors due to missing data)
		endpoints := []struct {
			method         string
			path           string
			expectedStatus int
		}{
			{"GET", "/health", 200},
			{"GET", "/management/images", 200},
			{"POST", "/cleanup", 400},    // Will fail due to missing JSON body, but route works
			{"GET", "/nonexistent", 404}, // Should return 404 for non-existent routes
		}

		for _, endpoint := range endpoints {
			var resp *http.Response
			var err error

			switch endpoint.method {
			case "GET":
				resp, err = http.Get(testServer.URL + endpoint.path)
			case "POST":
				resp, err = http.Post(testServer.URL+endpoint.path, "application/json", strings.NewReader(""))
			}

			if err != nil {
				t.Errorf("Request to %s %s failed: %v", endpoint.method, endpoint.path, err)
				continue
			}
			defer resp.Body.Close()

			if resp.StatusCode != endpoint.expectedStatus {
				t.Errorf("Expected status %d for %s %s, got %d",
					endpoint.expectedStatus, endpoint.method, endpoint.path, resp.StatusCode)
			}

			// All responses should have request ID from middleware
			if resp.Header.Get("X-Request-ID") == "" {
				t.Errorf("Expected X-Request-ID header for %s %s", endpoint.method, endpoint.path)
			}
		}
	})

	t.Run("WorkerPoolStats", func(t *testing.T) {
		stats := server.workerPool.GetStats()

		// Basic validation of worker pool stats
		if stats.ActiveWorkers < 0 {
			t.Error("Active workers count should not be negative")
		}

		if stats.QueuedJobs < 0 {
			t.Error("Queued jobs count should not be negative")
		}
	})
}
