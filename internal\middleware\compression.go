package middleware

import (
	"compress/gzip"
	"net/http"
	"strings"
)
// CompressionMiddleware provides gzip compression for HTTP responses
type CompressionMiddleware struct {
	compressionLevel int
}

// NewCompressionMiddleware creates a new compression middleware with specified compression level
func NewCompressionMiddleware(level int) *CompressionMiddleware {
	if level < gzip.NoCompression || level > gzip.BestCompression {
		level = gzip.DefaultCompression
	}
	return &CompressionMiddleware{
		compressionLevel: level,
	}
}

// gzipResponseWriter wraps http.ResponseWriter to provide gzip compression
type gzipResponseWriter struct {
	http.ResponseWriter
	gzipWriter *gzip.Writer
	headerWritten bool
}

// Write compresses and writes data to the response
func (grw *gzipResponseWriter) Write(data []byte) (int, error) {
	if !grw.headerWritten {
		grw.WriteHeader(http.StatusOK)
	}
	return grw.gzipWriter.Write(data)
}

// WriteHeader sets the response status code and compression headers
func (grw *gzipResponseWriter) WriteHeader(statusCode int) {
	if grw.headerWritten {
		return
	}
	grw.headerWritten = true
	
	// Set compression headers
	grw.Header().Set("Content-Encoding", "gzip")
	grw.Header().Set("Vary", "Accept-Encoding")
	grw.Header().Del("Content-Length") // Remove content-length as it will change after compression
	
	grw.ResponseWriter.WriteHeader(statusCode)
}

// Close closes the gzip writer and flushes any remaining data
func (grw *gzipResponseWriter) Close() error {
	if grw.gzipWriter != nil {
		return grw.gzipWriter.Close()
	}
	return nil
}

// Handler returns an HTTP handler that applies gzip compression
func (cm *CompressionMiddleware) Handler(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Check if client accepts gzip encoding
		if !cm.acceptsGzip(r) {
			next.ServeHTTP(w, r)
			return
		}
		
		// Check if response should be compressed based on content type
		if !cm.shouldCompress(r) {
			next.ServeHTTP(w, r)
			return
		}
		
		// Create gzip writer
		gzipWriter, err := gzip.NewWriterLevel(w, cm.compressionLevel)
		if err != nil {
			next.ServeHTTP(w, r)
			return
		}
		defer gzipWriter.Close()
		
		// Wrap response writer with gzip compression
		grw := &gzipResponseWriter{
			ResponseWriter: w,
			gzipWriter:     gzipWriter,
		}
		
		// Call next handler with compressed response writer
		next.ServeHTTP(grw, r)
	})
}

// acceptsGzip checks if the client accepts gzip encoding
func (cm *CompressionMiddleware) acceptsGzip(r *http.Request) bool {
	acceptEncoding := r.Header.Get("Accept-Encoding")
	return strings.Contains(strings.ToLower(acceptEncoding), "gzip")
}

// shouldCompress determines if the request should be compressed based on various factors
func (cm *CompressionMiddleware) shouldCompress(r *http.Request) bool {
	// Don't compress if it's already compressed
	if r.Header.Get("Content-Encoding") != "" {
		return false
	}
	
	// Don't compress WebSocket upgrades
	if r.Header.Get("Upgrade") != "" {
		return false
	}
	
	// Don't compress range requests
	if r.Header.Get("Range") != "" {
		return false
	}
	
	return true
}

// HandlerFunc returns a handler function that applies gzip compression
func (cm *CompressionMiddleware) HandlerFunc(next http.HandlerFunc) http.HandlerFunc {
	return cm.Handler(next).ServeHTTP
}

// DefaultCompressionMiddleware creates a compression middleware with default settings
func DefaultCompressionMiddleware() *CompressionMiddleware {
	return NewCompressionMiddleware(gzip.DefaultCompression)
}