# JavaScript Examples

Comprehensive JavaScript examples for integrating with the QR Background API.

## Basic Setup

### API Client Class
```javascript
class QRBackgroundAPI {
  constructor(baseUrl = 'http://localhost:8080') {
    this.baseUrl = baseUrl;
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const config = {
      headers: {
        'Accept': 'application/json',
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, config);
      
      // Handle binary responses (images)
      if (response.headers.get('content-type')?.startsWith('image/')) {
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        return {
          blob: await response.blob(),
          headers: Object.fromEntries(response.headers.entries())
        };
      }

      // Handle JSON responses
      const data = await response.json();
      
      if (!response.ok) {
        throw new APIError(data.error?.code || 'UNKNOWN_ERROR', 
                          data.error?.message || 'Unknown error', 
                          response.status, 
                          data.error?.details);
      }

      return data;
    } catch (error) {
      if (error instanceof APIError) {
        throw error;
      }
      throw new APIError('NETWORK_ERROR', error.message);
    }
  }

  // Upload image
  async uploadImage(file) {
    const formData = new FormData();
    formData.append('file', file);

    return this.request('/upload', {
      method: 'POST',
      body: formData
    });
  }

  // Generate QR code
  async generateQR(qrData) {
    return this.request('/generate-qr', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(qrData)
    });
  }

  // List images
  async listImages(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = queryString ? `/management/images?${queryString}` : '/management/images';
    return this.request(endpoint);
  }

  // Get image info
  async getImage(imageId) {
    return this.request(`/management/images/${imageId}`);
  }

  // Delete image
  async deleteImage(imageId) {
    return this.request(`/management/images/${imageId}`, {
      method: 'DELETE'
    });
  }

  // Cleanup
  async cleanup(options = {}) {
    return this.request('/cleanup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options)
    });
  }

  // Health check
  async healthCheck() {
    return this.request('/health');
  }
}

class APIError extends Error {
  constructor(code, message, status, details) {
    super(message);
    this.name = 'APIError';
    this.code = code;
    this.status = status;
    this.details = details;
  }
}
```

## Upload Examples

### Basic File Upload
```javascript
const api = new QRBackgroundAPI();

// Upload from file input
async function uploadFromInput(fileInput) {
  try {
    const file = fileInput.files[0];
    if (!file) {
      throw new Error('No file selected');
    }

    console.log(`Uploading ${file.name} (${file.size} bytes)`);
    
    const result = await api.uploadImage(file);
    console.log('Upload successful:', result.image_path);
    return result.image_path;
  } catch (error) {
    console.error('Upload failed:', error.message);
    throw error;
  }
}

// Usage with HTML file input
document.getElementById('fileInput').addEventListener('change', async (event) => {
  try {
    const imagePath = await uploadFromInput(event.target);
    document.getElementById('imagePath').textContent = imagePath;
  } catch (error) {
    document.getElementById('error').textContent = error.message;
  }
});
```

### Upload with Progress
```javascript
async function uploadWithProgress(file, onProgress) {
  return new Promise((resolve, reject) => {
    const formData = new FormData();
    formData.append('file', file);

    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const percentComplete = (event.loaded / event.total) * 100;
        onProgress(percentComplete);
      }
    });

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (error) {
          reject(new Error('Invalid JSON response'));
        }
      } else {
        reject(new Error(`Upload failed: ${xhr.status}`));
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Network error during upload'));
    });

    xhr.open('POST', 'http://localhost:8080/upload');
    xhr.send(formData);
  });
}

// Usage
const progressBar = document.getElementById('progressBar');
const file = document.getElementById('fileInput').files[0];

uploadWithProgress(file, (percent) => {
  progressBar.style.width = `${percent}%`;
  progressBar.textContent = `${Math.round(percent)}%`;
})
.then(result => {
  console.log('Upload complete:', result.image_path);
})
.catch(error => {
  console.error('Upload error:', error.message);
});
```

### Drag and Drop Upload
```javascript
class DragDropUploader {
  constructor(dropZone, api) {
    this.dropZone = dropZone;
    this.api = api;
    this.setupEventListeners();
  }

  setupEventListeners() {
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, this.preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, this.highlight.bind(this), false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
      this.dropZone.addEventListener(eventName, this.unhighlight.bind(this), false);
    });

    this.dropZone.addEventListener('drop', this.handleDrop.bind(this), false);
  }

  preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  }

  highlight() {
    this.dropZone.classList.add('drag-over');
  }

  unhighlight() {
    this.dropZone.classList.remove('drag-over');
  }

  async handleDrop(e) {
    const files = Array.from(e.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) {
      alert('Please drop image files only');
      return;
    }

    for (const file of imageFiles) {
      try {
        console.log(`Uploading ${file.name}...`);
        const result = await this.api.uploadImage(file);
        console.log(`Uploaded: ${result.image_path}`);
        this.onUploadSuccess(result);
      } catch (error) {
        console.error(`Failed to upload ${file.name}:`, error.message);
        this.onUploadError(file.name, error);
      }
    }
  }

  onUploadSuccess(result) {
    // Override this method to handle successful uploads
    console.log('Upload successful:', result);
  }

  onUploadError(filename, error) {
    // Override this method to handle upload errors
    console.error(`Upload failed for ${filename}:`, error.message);
  }
}

// Usage
const api = new QRBackgroundAPI();
const dropZone = document.getElementById('dropZone');
const uploader = new DragDropUploader(dropZone, api);

uploader.onUploadSuccess = (result) => {
  const imageList = document.getElementById('uploadedImages');
  const item = document.createElement('div');
  item.textContent = `Uploaded: ${result.image_path}`;
  imageList.appendChild(item);
};
```

## QR Generation Examples

### Basic QR Generation
```javascript
async function generateQRCode(data, imagePath, position, size, format = 'png') {
  const api = new QRBackgroundAPI();
  
  try {
    const qrRequest = {
      data: data,
      image_path: imagePath,
      x: position.x,
      y: position.y,
      width: size.width,
      height: size.height,
      output_format: format
    };

    console.log('Generating QR code...', qrRequest);
    
    const result = await api.generateQR(qrRequest);
    
    // Create image element to display result
    const img = document.createElement('img');
    const url = URL.createObjectURL(result.blob);
    img.src = url;
    img.onload = () => URL.revokeObjectURL(url); // Clean up
    
    document.getElementById('qrResult').appendChild(img);
    
    console.log(`QR generated in ${result.headers['x-processing-time-ms']}ms`);
    return result;
  } catch (error) {
    console.error('QR generation failed:', error.message);
    throw error;
  }
}

// Usage
generateQRCode(
  'https://example.com',
  'images/347/abc123def456.jpg',
  { x: 100, y: 100 },
  { width: 200, height: 200 },
  'png'
);
```

### Interactive QR Generator
```javascript
class InteractiveQRGenerator {
  constructor(canvasId, api) {
    this.canvas = document.getElementById(canvasId);
    this.ctx = this.canvas.getContext('2d');
    this.api = api;
    this.backgroundImage = null;
    this.qrPosition = { x: 100, y: 100 };
    this.qrSize = { width: 200, height: 200 };
    
    this.setupEventListeners();
  }

  setupEventListeners() {
    this.canvas.addEventListener('click', this.handleCanvasClick.bind(this));
    this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
  }

  async loadBackgroundImage(imagePath) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.backgroundImage = img;
        this.canvas.width = img.width;
        this.canvas.height = img.height;
        this.drawCanvas();
        resolve(img);
      };
      img.onerror = reject;
      img.src = `/storage/${imagePath}`; // Adjust path as needed
    });
  }

  drawCanvas() {
    if (!this.backgroundImage) return;

    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Draw background image
    this.ctx.drawImage(this.backgroundImage, 0, 0);
    
    // Draw QR position indicator
    this.ctx.strokeStyle = 'red';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(
      this.qrPosition.x, 
      this.qrPosition.y, 
      this.qrSize.width, 
      this.qrSize.height
    );
  }

  handleCanvasClick(event) {
    const rect = this.canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    this.qrPosition = { x: x - this.qrSize.width / 2, y: y - this.qrSize.height / 2 };
    this.drawCanvas();
  }

  handleMouseMove(event) {
    const rect = this.canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Update cursor style
    this.canvas.style.cursor = 'crosshair';
  }

  async generateQR(data, format = 'png') {
    if (!this.backgroundImage) {
      throw new Error('No background image loaded');
    }

    try {
      const result = await this.api.generateQR({
        data: data,
        image_path: this.imagePath,
        x: Math.max(0, this.qrPosition.x),
        y: Math.max(0, this.qrPosition.y),
        width: this.qrSize.width,
        height: this.qrSize.height,
        output_format: format
      });

      // Display result
      const resultImg = document.createElement('img');
      const url = URL.createObjectURL(result.blob);
      resultImg.src = url;
      resultImg.onload = () => URL.revokeObjectURL(url);
      
      document.getElementById('generatedQR').innerHTML = '';
      document.getElementById('generatedQR').appendChild(resultImg);
      
      return result;
    } catch (error) {
      console.error('QR generation failed:', error);
      throw error;
    }
  }
}

// Usage
const api = new QRBackgroundAPI();
const generator = new InteractiveQRGenerator('qrCanvas', api);

// Load background image
document.getElementById('imageSelect').addEventListener('change', async (event) => {
  const imagePath = event.target.value;
  if (imagePath) {
    generator.imagePath = imagePath;
    await generator.loadBackgroundImage(imagePath);
  }
});

// Generate QR button
document.getElementById('generateBtn').addEventListener('click', async () => {
  const qrData = document.getElementById('qrData').value;
  if (qrData) {
    try {
      await generator.generateQR(qrData);
    } catch (error) {
      alert(`QR generation failed: ${error.message}`);
    }
  }
});
```

### Batch QR Generation
```javascript
async function generateMultipleQRs(qrConfigs) {
  const api = new QRBackgroundAPI();
  const results = [];
  
  for (let i = 0; i < qrConfigs.length; i++) {
    const config = qrConfigs[i];
    
    try {
      console.log(`Generating QR ${i + 1}/${qrConfigs.length}...`);
      
      const result = await api.generateQR(config);
      
      // Create download link
      const url = URL.createObjectURL(result.blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `qr-${i + 1}.${config.output_format}`;
      link.textContent = `Download QR ${i + 1}`;
      link.onclick = () => setTimeout(() => URL.revokeObjectURL(url), 100);
      
      document.getElementById('downloadLinks').appendChild(link);
      document.getElementById('downloadLinks').appendChild(document.createElement('br'));
      
      results.push({ index: i, result, config });
      
      // Update progress
      const progress = ((i + 1) / qrConfigs.length) * 100;
      document.getElementById('batchProgress').style.width = `${progress}%`;
      
    } catch (error) {
      console.error(`Failed to generate QR ${i + 1}:`, error.message);
      results.push({ index: i, error, config });
    }
  }
  
  return results;
}

// Usage
const qrConfigs = [
  {
    data: 'https://example.com/page1',
    image_path: 'images/347/background.jpg',
    x: 100, y: 100, width: 200, height: 200,
    output_format: 'png'
  },
  {
    data: 'https://example.com/page2',
    image_path: 'images/347/background.jpg',
    x: 300, y: 100, width: 200, height: 200,
    output_format: 'png'
  },
  {
    data: 'Contact: ******-0123',
    image_path: 'images/347/background.jpg',
    x: 500, y: 100, width: 200, height: 200,
    output_format: 'png'
  }
];

document.getElementById('batchGenerateBtn').addEventListener('click', async () => {
  try {
    const results = await generateMultipleQRs(qrConfigs);
    console.log('Batch generation complete:', results);
  } catch (error) {
    console.error('Batch generation failed:', error);
  }
});
```

## Management Examples

### Image Gallery
```javascript
class ImageGallery {
  constructor(containerId, api) {
    this.container = document.getElementById(containerId);
    this.api = api;
    this.currentPage = 1;
    this.itemsPerPage = 12;
    this.images = [];
  }

  async loadImages(page = 1, limit = this.itemsPerPage) {
    try {
      const response = await this.api.listImages({ page, limit });
      this.images = response.data.images;
      this.pagination = response.data.pagination;
      this.renderGallery();
      this.renderPagination();
    } catch (error) {
      console.error('Failed to load images:', error.message);
      this.container.innerHTML = `<p>Error loading images: ${error.message}</p>`;
    }
  }

  renderGallery() {
    this.container.innerHTML = '';
    
    this.images.forEach(image => {
      const imageCard = this.createImageCard(image);
      this.container.appendChild(imageCard);
    });
  }

  createImageCard(image) {
    const card = document.createElement('div');
    card.className = 'image-card';
    
    const sizeInMB = (image.file_size / (1024 * 1024)).toFixed(2);
    const createdDate = new Date(image.created_at).toLocaleDateString();
    
    card.innerHTML = `
      <div class="image-info">
        <h3>${image.original_name}</h3>
        <p>Size: ${sizeInMB} MB</p>
        <p>Created: ${createdDate}</p>
        <p>Location: ${image.location}</p>
        <p>Path: ${image.image_path}</p>
      </div>
      <div class="image-actions">
        <button onclick="this.parentElement.parentElement.selectImage('${image.id}')">
          Select for QR
        </button>
        <button onclick="this.parentElement.parentElement.deleteImage('${image.id}')" 
                class="delete-btn">
          Delete
        </button>
      </div>
    `;
    
    // Add methods to the card element
    card.selectImage = (imageId) => this.selectImage(imageId);
    card.deleteImage = (imageId) => this.deleteImage(imageId);
    
    return card;
  }

  async selectImage(imageId) {
    try {
      const imageInfo = await this.api.getImage(imageId);
      
      // Trigger custom event
      const event = new CustomEvent('imageSelected', {
        detail: { image: imageInfo.data }
      });
      document.dispatchEvent(event);
      
      console.log('Image selected:', imageInfo.data.image_path);
    } catch (error) {
      console.error('Failed to select image:', error.message);
    }
  }

  async deleteImage(imageId) {
    if (!confirm('Are you sure you want to delete this image?')) {
      return;
    }

    try {
      await this.api.deleteImage(imageId);
      
      // Reload current page
      await this.loadImages(this.currentPage);
      
      console.log('Image deleted successfully');
    } catch (error) {
      console.error('Failed to delete image:', error.message);
      alert(`Delete failed: ${error.message}`);
    }
  }

  renderPagination() {
    const paginationContainer = document.getElementById('pagination');
    if (!paginationContainer || !this.pagination) return;

    paginationContainer.innerHTML = '';

    // Previous button
    if (this.pagination.has_prev) {
      const prevBtn = document.createElement('button');
      prevBtn.textContent = 'Previous';
      prevBtn.onclick = () => this.loadImages(this.pagination.page - 1);
      paginationContainer.appendChild(prevBtn);
    }

    // Page info
    const pageInfo = document.createElement('span');
    pageInfo.textContent = ` Page ${this.pagination.page} of ${this.pagination.total_pages} `;
    paginationContainer.appendChild(pageInfo);

    // Next button
    if (this.pagination.has_next) {
      const nextBtn = document.createElement('button');
      nextBtn.textContent = 'Next';
      nextBtn.onclick = () => this.loadImages(this.pagination.page + 1);
      paginationContainer.appendChild(nextBtn);
    }
  }
}

// Usage
const api = new QRBackgroundAPI();
const gallery = new ImageGallery('imageGallery', api);

// Load images on page load
gallery.loadImages();

// Listen for image selection
document.addEventListener('imageSelected', (event) => {
  const selectedImage = event.detail.image;
  document.getElementById('selectedImagePath').value = selectedImage.image_path;
  console.log('Selected image for QR generation:', selectedImage.image_path);
});
```
