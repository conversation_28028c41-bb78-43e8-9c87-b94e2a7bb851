package qr

import (
	"bytes"
	"fmt"
	"image"
	"image/png"

	"github.com/skip2/go-qrcode"
	"qr-background-api/internal/interfaces"
)

// Generator implements the QRGenerator interface
type Generator struct{}

// NewGenerator creates a new QR code generator
func NewGenerator() interfaces.QRGenerator {
	return &Generator{}
}

// Generate creates a QR code image with specified dimensions
func (g *Generator) Generate(data string, width, height int) (image.Image, error) {
	if data == "" {
		return nil, fmt.Errorf("QR data cannot be empty")
	}
	
	if width <= 0 || height <= 0 {
		return nil, fmt.Errorf("QR dimensions must be positive: width=%d, height=%d", width, height)
	}
	
	// Generate QR code with the specified dimensions
	// go-qrcode uses the size parameter as both width and height, so we'll use the smaller dimension
	// to ensure the QR code fits within the specified bounds
	size := width
	if height < width {
		size = height
	}
	
	qrCode, err := qrcode.New(data, qrcode.Medium)
	if err != nil {
		return nil, fmt.E<PERSON><PERSON>("failed to create QR code: %w", err)
	}
	
	// Generate the image with the calculated size
	img := qrCode.Image(size)
	
	return img, nil
}

// GenerateToBuffer creates a QR code and writes it directly to a buffer for memory efficiency
func (g *Generator) GenerateToBuffer(data string, width, height int, buf *bytes.Buffer) error {
	if buf == nil {
		return fmt.Errorf("buffer cannot be nil")
	}
	
	// Generate the QR code image
	img, err := g.Generate(data, width, height)
	if err != nil {
		return fmt.Errorf("failed to generate QR code: %w", err)
	}
	
	// Encode the image to PNG format and write to buffer
	if err := png.Encode(buf, img); err != nil {
		return fmt.Errorf("failed to encode QR code to buffer: %w", err)
	}
	
	return nil
}