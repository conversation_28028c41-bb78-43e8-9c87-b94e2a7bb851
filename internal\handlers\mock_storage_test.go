package handlers

import (
	"errors"
	"image"
	"mime/multipart"
	"qr-background-api/internal/interfaces"
)

// MockStorageManager is a mock implementation of StorageManager for testing
type MockStorageManager struct {
	ShouldFail bool
	SavedImages map[string]image.Image
	DeletedImages []string
	MovedToCloud []string
	FetchedFromCloud []string
}

// NewMockStorageManager creates a new mock storage manager
func NewMockStorageManager() *MockStorageManager {
	return &MockStorageManager{
		SavedImages: make(map[string]image.Image),
		DeletedImages: make([]string, 0),
		MovedToCloud: make([]string, 0),
		FetchedFromCloud: make([]string, 0),
	}
}

// SaveImage saves an uploaded image file and returns the storage path
func (m *MockStorageManager) SaveImage(file multipart.File) (string, error) {
	if m.ShouldFail {
		return "", errors.New("mock save error")
	}
	return "mock/path/image.jpg", nil
}

// LoadImage loads an image from the given path
func (m *MockStorageManager) LoadImage(path string) (image.Image, error) {
	if m.ShouldFail {
		return nil, errors.New("mock load error")
	}
	if img, exists := m.SavedImages[path]; exists {
		return img, nil
	}
	// Return a simple mock image
	return image.NewRGBA(image.Rect(0, 0, 100, 100)), nil
}

// DeleteImage removes an image from storage
func (m *MockStorageManager) DeleteImage(path string) error {
	if m.ShouldFail {
		return errors.New("mock delete error")
	}
	m.DeletedImages = append(m.DeletedImages, path)
	return nil
}

// MoveToCloud moves an image from local storage to cloud storage
func (m *MockStorageManager) MoveToCloud(path string) error {
	if m.ShouldFail {
		return errors.New("mock move to cloud error")
	}
	m.MovedToCloud = append(m.MovedToCloud, path)
	return nil
}

// FetchFromCloud retrieves an image from cloud storage to local cache
func (m *MockStorageManager) FetchFromCloud(path string) error {
	if m.ShouldFail {
		return errors.New("mock fetch from cloud error")
	}
	m.FetchedFromCloud = append(m.FetchedFromCloud, path)
	return nil
}

// GenerateUniqueID creates a unique identifier for image storage
func (m *MockStorageManager) GenerateUniqueID() string {
	return "mock-unique-id-12345"
}

// GetFolderFromID determines which numbered folder (1-1000) to use for an ID
func (m *MockStorageManager) GetFolderFromID(id string) int {
	return 1 // Always return folder 1 for testing
}

// Verify interface compliance
var _ interfaces.StorageManager = (*MockStorageManager)(nil)