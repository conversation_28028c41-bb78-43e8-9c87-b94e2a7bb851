package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"qr-background-api/internal/config"
	"qr-background-api/internal/errors"
	"qr-background-api/internal/interfaces"
	"qr-background-api/internal/logging"
	"qr-background-api/internal/timeout"
)

// QRGenerationHandler handles QR code generation requests
type QRGenerationHandler struct {
	workerPool interfaces.WorkerPool
	config     *config.Config
}

// NewQRGenerationHandler creates a new QRGenerationHandler instance
func NewQRGenerationHandler(workerPool interfaces.WorkerPool, cfg *config.Config) *QRGenerationHandler {
	return &QRGenerationHandler{
		workerPool: workerPool,
		config:     cfg,
	}
}

// QRGenerationRequest represents the QR generation request structure
type QRGenerationRequest struct {
	Data         string `json:"data"`
	ImagePath    string `json:"image_path"`
	X            int    `json:"x"`
	Y            int    `json:"y"`
	Width        int    `json:"width"`
	Height       int    `json:"height"`
	OutputFormat string `json:"output_format"`
}

// QRGenerationResponse represents the QR generation response structure
type QRGenerationResponse struct {
	Success      bool   `json:"success"`
	Message      string `json:"message,omitempty"`
	ImageData    []byte `json:"image_data,omitempty"`
	ContentType  string `json:"content_type,omitempty"`
	ProcessingMs int64  `json:"processing_ms,omitempty"`
}

// Legacy APIError type for backward compatibility

// Error definitions for QR generation
var (
	ErrQRInvalidRequest        = errors.NewAPIError("QR_INVALID_REQUEST", "Invalid QR generation request", 400)
	ErrQRMissingData           = errors.NewAPIError("QR_MISSING_DATA", "QR data is required", 400)
	ErrQRMissingImagePath      = errors.NewAPIError("QR_MISSING_IMAGE_PATH", "Image path is required", 400)
	ErrQRInvalidDimensions     = errors.NewAPIError("QR_INVALID_DIMENSIONS", "Invalid QR dimensions", 400)
	ErrQRInvalidPosition       = errors.NewAPIError("QR_INVALID_POSITION", "Invalid QR position", 400)
	ErrQRInvalidFormat         = errors.NewAPIError("QR_INVALID_FORMAT", "Invalid output format", 400)
	ErrQRGenerationFailed      = errors.NewAPIError("QR_GENERATION_FAILED", "QR generation failed", 500)
	ErrQRProcessingTimeout     = errors.NewAPIError("QR_PROCESSING_TIMEOUT", "QR generation timeout", 500)
	ErrQRWorkerPoolUnavailable = errors.NewAPIError("QR_WORKER_POOL_UNAVAILABLE", "Service temporarily unavailable", 503)
)

// ServeHTTP implements the http.Handler interface for QR generation endpoint
func (h *QRGenerationHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(r.Context(), timeout.GetManager().GetConfig().QRGeneration)
	defer cancel()
	r = r.WithContext(ctx)

	// Get logger with request context
	logger := logging.GetLogger().WithContext(ctx).WithFields(map[string]interface{}{
		"handler": "qr_generation",
		"method":  r.Method,
		"path":    r.URL.Path,
	})

	logger.Info("Processing QR generation request")
	startTime := time.Now()

	// Set response headers
	w.Header().Set("Content-Type", "application/json")
	SetCORSHeaders(w)

	// Handle preflight requests
	if HandlePreflight(w, r) {
		return
	}

	// Only allow POST method
	if !ValidateMethod(w, r, http.MethodPost) {
		return
	}

	// Check if worker pool is running
	if !h.workerPool.IsRunning() {
		logger.Error("Worker pool is not running")
		apiErr := ErrQRWorkerPoolUnavailable.WithDetails("Worker pool is not running")
		errors.WriteErrorResponse(w, r, apiErr)
		return
	}

	// Parse JSON request
	var req QRGenerationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logger.WithError(err).Warn("Failed to parse JSON request")
		apiErr := ErrQRInvalidRequest.WithDetails(err.Error())
		errors.WriteErrorResponse(w, r, apiErr)
		return
	}

	logger.WithFields(map[string]interface{}{
		"data_length": len(req.Data),
		"image_path":  req.ImagePath,
		"dimensions":  fmt.Sprintf("%dx%d", req.Width, req.Height),
		"position":    fmt.Sprintf("(%d,%d)", req.X, req.Y),
		"format":      req.OutputFormat,
	}).Debug("QR generation request details")

	// Validate request
	if err := h.validateRequest(&req); err != nil {
		logger.WithError(err).Warn("Request validation failed")
		errors.WriteErrorResponse(w, r, err)
		return
	}

	// Create QR job
	qrJob := interfaces.QRJob{
		Request: interfaces.QRRequest{
			Data:         req.Data,
			ImagePath:    req.ImagePath,
			X:            req.X,
			Y:            req.Y,
			Width:        req.Width,
			Height:       req.Height,
			OutputFormat: req.OutputFormat,
		},
	}

	// Submit job to worker pool
	resultChan := h.workerPool.Submit(qrJob)

	// Wait for result with timeout
	select {
	case result := <-resultChan:
		processingTime := time.Since(startTime)

		if result.Error != nil {
			logger.WithError(result.Error).WithFields(map[string]interface{}{
				"processing_time_ms": processingTime.Milliseconds(),
			}).Error("QR generation failed")

			// Handle specific error types
			if strings.Contains(result.Error.Error(), "image not found") {
				apiErr := errors.NewAPIError("IMAGE_NOT_FOUND", "Background image not found", 404).WithDetails(result.Error.Error())
				errors.WriteErrorResponse(w, r, apiErr)
			} else if strings.Contains(result.Error.Error(), "dimensions exceed") {
				apiErr := errors.NewAPIError("DIMENSIONS_EXCEED", "QR dimensions exceed background boundaries", 400).WithDetails(result.Error.Error())
				errors.WriteErrorResponse(w, r, apiErr)
			} else {
				apiErr := ErrQRGenerationFailed.WithDetails(result.Error.Error())
				errors.WriteErrorResponse(w, r, apiErr)
			}
			return
		}

		logger.WithFields(map[string]interface{}{
			"processing_time_ms": processingTime.Milliseconds(),
			"image_size_bytes":   len(result.ImageData),
		}).Info("QR generation completed successfully")

		// Success - return image data
		h.writeImageResponse(w, result.ImageData, req.OutputFormat, processingTime.Milliseconds())

	case <-ctx.Done():
		logger.Error("QR generation timeout")
		apiErr := ErrQRProcessingTimeout.WithDetails("Processing exceeded timeout limit")
		errors.WriteErrorResponse(w, r, apiErr)
	case <-time.After(5 * time.Second): // Reasonable timeout for testing
		logger.Warn("QR generation exceeded reasonable timeout")
		apiErr := ErrQRProcessingTimeout.WithDetails("Processing exceeded reasonable timeout")
		errors.WriteErrorResponse(w, r, apiErr)
	}
}

// validateRequest validates the QR generation request
func (h *QRGenerationHandler) validateRequest(req *QRGenerationRequest) *errors.APIError {
	// Validate required fields
	if req.Data == "" {
		return ErrQRMissingData.WithDetails("Data field cannot be empty")
	}

	if req.ImagePath == "" {
		return ErrQRMissingImagePath.WithDetails("image_path field cannot be empty")
	}

	// Validate dimensions
	if req.Width <= 0 || req.Height <= 0 {
		return ErrQRInvalidDimensions.WithDetails("Width and height must be positive integers")
	}

	// Validate position
	if req.X < 0 || req.Y < 0 {
		return ErrQRInvalidPosition.WithDetails("X and Y coordinates must be non-negative")
	}

	// Validate output format
	if req.OutputFormat == "" {
		req.OutputFormat = "png" // Default format
	} else {
		format := strings.ToLower(req.OutputFormat)
		if format != "jpeg" && format != "jpg" && format != "png" && format != "webp" {
			return ErrQRInvalidFormat.WithDetails("Supported formats: jpeg, png, webp")
		}
		req.OutputFormat = format
	}

	return nil
}

// writeImageResponse writes the generated image as response
func (h *QRGenerationHandler) writeImageResponse(w http.ResponseWriter, imageData []byte, format string, processingMs int64) {
	// Determine content type based on format
	var contentType string
	switch strings.ToLower(format) {
	case "jpeg", "jpg":
		contentType = "image/jpeg"
	case "png":
		contentType = "image/png"
	case "webp":
		contentType = "image/webp"
	default:
		contentType = "image/png"
	}

	// Set headers
	w.Header().Set("Content-Type", contentType)
	w.Header().Set("Content-Length", strconv.Itoa(len(imageData)))
	w.Header().Set("X-Processing-Time-Ms", strconv.FormatInt(processingMs, 10))
	w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")
	w.WriteHeader(http.StatusOK)

	// Write image data
	w.Write(imageData)
}

// writeErrorResponse writes an error response (deprecated - use errors.WriteErrorResponse)
func (h *QRGenerationHandler) writeErrorResponse(w http.ResponseWriter, statusCode int, apiError *errors.APIError) {
	response := ErrorResponse{
		Success: false,
		Error:   apiError.Message,
		Code:    statusCode,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}
