package interfaces

import (
	"bytes"
	"image"
)

// QRGenerator handles QR code generation
type QRGenerator interface {
	// Generate creates a QR code image with specified dimensions
	Generate(data string, width, height int) (image.Image, error)
	
	// GenerateToBuffer creates a QR code and writes it directly to a buffer for memory efficiency
	GenerateToBuffer(data string, width, height int, buf *bytes.Buffer) error
}