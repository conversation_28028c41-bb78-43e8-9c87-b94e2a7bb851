package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"qr-background-api/internal/config"
	"qr-background-api/internal/errors"
	"qr-background-api/internal/interfaces"
	"qr-background-api/internal/logging"
	"qr-background-api/internal/timeout"
)

// UploadHandler handles image upload requests
type UploadHandler struct {
	storageManager interfaces.StorageManager
	config         *config.Config
}

// NewUploadHandler creates a new UploadHandler instance
func NewUploadHandler(storageManager interfaces.StorageManager, cfg *config.Config) *UploadHandler {
	return &UploadHandler{
		storageManager: storageManager,
		config:         cfg,
	}
}

// UploadRequest represents the upload request structure
type UploadRequest struct {
	File interface{} `form:"file"`
}

// UploadResponse represents the upload response structure
type UploadResponse struct {
	ImagePath string `json:"image_path"`
	Success   bool   `json:"success"`
	Message   string `json:"message,omitempty"`
}

// Legacy APIError type for backward compatibility

// ServeHTTP implements the http.Handler interface for upload endpoint
func (h *UploadHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(r.Context(), timeout.GetManager().GetConfig().FileUpload)
	defer cancel()
	r = r.WithContext(ctx)

	// Get logger with request context
	logger := logging.GetLogger().WithContext(ctx).WithFields(map[string]interface{}{
		"handler": "upload",
		"method":  r.Method,
		"path":    r.URL.Path,
	})

	logger.Info("Processing upload request")
	startTime := time.Now()

	// Set response headers
	w.Header().Set("Content-Type", "application/json")

	// Only allow POST method
	if r.Method != http.MethodPost {
		err := errors.NewAPIError("METHOD_NOT_ALLOWED", "Method not allowed", 405).WithDetails("Only POST method is supported")
		logger.WithError(err).Warn("Invalid HTTP method")
		errors.WriteErrorResponse(w, r, err)
		return
	}

	// Parse multipart form with size limit
	maxFileSize := h.config.Storage.MaxFileSize
	if maxFileSize == 0 {
		maxFileSize = 10 << 20 // Default 10MB
	}

	logger.WithFields(map[string]interface{}{"max_file_size": maxFileSize}).Debug("Parsing multipart form")

	err := r.ParseMultipartForm(maxFileSize)
	if err != nil {
		logger.WithError(err).Error("Failed to parse multipart form")
		if strings.Contains(err.Error(), "request body too large") {
			apiErr := errors.ErrFileTooLarge.WithDetails(fmt.Sprintf("Maximum file size: %d bytes", maxFileSize))
			errors.WriteErrorResponse(w, r, apiErr)
			return
		}
		apiErr := errors.NewAPIError("INVALID_REQUEST", "Invalid request format", 400).WithDetails(err.Error())
		errors.WriteErrorResponse(w, r, apiErr)
		return
	}

	// Get the file from form
	file, fileHeader, err := r.FormFile("file")
	if err != nil {
		logger.WithError(err).Warn("No file provided in request")
		apiErr := errors.NewAPIError("NO_FILE_PROVIDED", "Please provide a file in the 'file' field", 400).WithDetails(err.Error())
		errors.WriteErrorResponse(w, r, apiErr)
		return
	}
	defer file.Close()

	logger.WithFields(map[string]interface{}{
		"filename":     fileHeader.Filename,
		"size":         fileHeader.Size,
		"content_type": fileHeader.Header.Get("Content-Type"),
	}).Info("File received")

	// Validate file size
	if fileHeader.Size > maxFileSize {
		logger.WithFields(map[string]interface{}{
			"file_size": fileHeader.Size,
			"max_size":  maxFileSize,
		}).Warn("File size exceeds limit")
		apiErr := errors.ErrFileTooLarge.WithDetails(fmt.Sprintf("File size: %d bytes, Maximum: %d bytes", fileHeader.Size, maxFileSize))
		errors.WriteErrorResponse(w, r, apiErr)
		return
	}

	// Validate file format by checking content type
	contentType := fileHeader.Header.Get("Content-Type")
	if err := errors.ValidateImageFormat(contentType); err != nil {
		logger.WithFields(map[string]interface{}{"content_type": contentType}).Warn("Invalid image format")
		if apiErr, ok := err.(*errors.APIError); ok {
			errors.WriteErrorResponse(w, r, apiErr)
		} else {
			apiErr := errors.ErrInvalidImageFormat.WithDetails(err.Error())
			errors.WriteErrorResponse(w, r, apiErr)
		}
		return
	}

	// Save the image using storage manager with timeout
	var imagePath string
	err = timeout.GetManager().WithFileUploadTimeout(ctx, "save_image", func(ctx context.Context) error {
		var saveErr error
		imagePath, saveErr = h.storageManager.SaveImage(file)
		return saveErr
	})

	if err != nil {
		logger.WithError(err).Error("Failed to save image")
		// Check if it's a format validation error from storage manager
		if strings.Contains(err.Error(), "unsupported image format") {
			apiErr := errors.ErrInvalidImageFormat.WithDetails(err.Error())
			errors.WriteErrorResponse(w, r, apiErr)
			return
		}
		// Check for timeout error
		if errors.IsRetryableError(err) {
			apiErr := errors.ErrTimeout.WithDetails("File upload timeout")
			errors.WriteErrorResponse(w, r, apiErr)
			return
		}
		apiErr := errors.ErrStorageFailed.WithDetails(err.Error())
		errors.WriteErrorResponse(w, r, apiErr)
		return
	}

	// Log successful upload
	processingTime := time.Since(startTime)
	logger.WithFields(map[string]interface{}{
		"image_path":         imagePath,
		"processing_time_ms": processingTime.Milliseconds(),
	}).Info("Image uploaded successfully")

	// Return success response with direct file path
	response := UploadResponse{
		ImagePath: imagePath,
		Success:   true,
		Message:   "Image uploaded successfully",
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// isValidImageFormat checks if the content type is a supported image format
func (h *UploadHandler) isValidImageFormat(contentType string) bool {
	supportedFormats := []string{
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/webp",
	}

	for _, format := range supportedFormats {
		if contentType == format {
			return true
		}
	}
	return false
}

// writeErrorResponse writes an error response in JSON format (deprecated - use errors.WriteErrorResponse)
func (h *UploadHandler) writeErrorResponse(w http.ResponseWriter, statusCode int, apiError *errors.APIError) {
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(apiError)
}

// GetMaxFileSize returns the maximum file size in a human-readable format
func (h *UploadHandler) GetMaxFileSize() string {
	maxSize := h.config.Storage.MaxFileSize
	if maxSize == 0 {
		maxSize = 10 << 20 // Default 10MB
	}
	return formatFileSize(maxSize)
}

// formatFileSize converts bytes to human-readable format
func formatFileSize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}
