# Performance Guide

Performance characteristics, optimization strategies, and benchmarking for the QR Background API.

## Performance Requirements

### Core Performance Targets
- **QR Generation**: < 80ms processing time (hard requirement)
- **File Upload**: < 5 seconds for 10MB files
- **Image Listing**: < 500ms for paginated results
- **Health Check**: < 100ms response time
- **Concurrent Requests**: Support 100+ concurrent QR generations

### Throughput Expectations
- **QR Generation**: 500+ QR codes per minute
- **File Uploads**: 50+ uploads per minute
- **API Requests**: 1000+ requests per minute

## Architecture Performance Features

### Worker Pool System
```yaml
worker_pool:
  size: 10                    # Concurrent workers
  queue_size: 100             # Job queue capacity
  buffer_pool_size: 50        # Reusable buffers
```

**Benefits:**
- Parallel QR generation processing
- Queue-based load balancing
- Memory-efficient buffer reuse
- Graceful degradation under load

### Memory Optimization
```yaml
performance:
  enable_memory_optimization: true
  memory_limit: 536870912     # 512MB
  gc_interval_seconds: 30
  force_gc_threshold: 268435456  # 256MB
```

**Features:**
- Automatic garbage collection
- Memory usage monitoring
- Streaming for large responses
- Buffer pooling for frequent operations

### Compression and Streaming
```yaml
performance:
  enable_compression: true
  compression_level: 6
  enable_streaming: true
  stream_buffer_size: 32768   # 32KB
```

**Optimizations:**
- Gzip compression for JSON responses
- Streaming for image uploads/downloads
- Configurable buffer sizes
- Minimal memory footprint

## Performance Monitoring

### Built-in Metrics

#### Response Headers
```http
X-Processing-Time-Ms: 65
X-Request-ID: uuid-string
Content-Length: 45678
```

#### Health Check Metrics
```json
{
  "checks": {
    "worker_pool": {
      "active_workers": 10,
      "queue_size": 0,
      "processed_jobs": 1250
    },
    "memory": {
      "used_mb": 128,
      "available_mb": 384,
      "gc_runs": 45
    }
  }
}
```

### Performance Logging
The API logs performance metrics for all operations:
- Request/response times
- Memory usage patterns
- Worker pool utilization
- Error rates and patterns

## Optimization Strategies

### 1. Worker Pool Tuning

#### Small Load (< 10 concurrent requests)
```yaml
worker_pool:
  size: 5
  queue_size: 25
  buffer_pool_size: 10
```

#### Medium Load (10-50 concurrent requests)
```yaml
worker_pool:
  size: 15
  queue_size: 150
  buffer_pool_size: 50
```

#### High Load (50+ concurrent requests)
```yaml
worker_pool:
  size: 30
  queue_size: 300
  buffer_pool_size: 100
```

### 2. Memory Configuration

#### Memory-Constrained Environment
```yaml
performance:
  memory_limit: 268435456      # 256MB
  gc_interval_seconds: 10      # Frequent GC
  force_gc_threshold: 134217728 # 128MB
  stream_buffer_size: 16384    # 16KB
```

#### High-Memory Environment
```yaml
performance:
  memory_limit: 2147483648     # 2GB
  gc_interval_seconds: 60      # Less frequent GC
  force_gc_threshold: 536870912 # 512MB
  stream_buffer_size: 65536    # 64KB
```

### 3. Storage Optimization

#### Fast Local Storage
```yaml
storage:
  local_path: "/fast-ssd/storage"  # Use SSD storage
  max_file_size: 5242880           # 5MB limit for speed
```

#### Network Storage Considerations
- Use local SSD for active files
- Move old files to slower network storage
- Configure appropriate timeouts for network operations

## Benchmarking

### QR Generation Performance Test
```bash
#!/bin/bash
# Test QR generation performance

BASE_URL="http://localhost:8080"
IMAGE_PATH="images/347/test-background.jpg"
CONCURRENT_REQUESTS=10
TOTAL_REQUESTS=100

echo "Testing QR generation performance..."
echo "Concurrent requests: $CONCURRENT_REQUESTS"
echo "Total requests: $TOTAL_REQUESTS"

# Use Apache Bench (ab) for load testing
ab -n $TOTAL_REQUESTS -c $CONCURRENT_REQUESTS \
   -p qr_request.json \
   -T "application/json" \
   "$BASE_URL/generate-qr"
```

### Sample qr_request.json
```json
{
  "data": "https://example.com/performance-test",
  "image_path": "images/347/test-background.jpg",
  "x": 100,
  "y": 100,
  "width": 200,
  "height": 200,
  "output_format": "png"
}
```

### Upload Performance Test
```bash
#!/bin/bash
# Test upload performance

BASE_URL="http://localhost:8080"
TEST_FILE="test-image.jpg"
CONCURRENT_UPLOADS=5
TOTAL_UPLOADS=50

echo "Testing upload performance..."

# Create test file if it doesn't exist
if [ ! -f "$TEST_FILE" ]; then
  # Create a 1MB test image (requires ImageMagick)
  convert -size 1000x1000 xc:white "$TEST_FILE"
fi

# Sequential uploads
echo "Sequential uploads:"
time for i in $(seq 1 10); do
  curl -s -X POST "$BASE_URL/upload" \
    -F "file=@$TEST_FILE" \
    -o /dev/null
done

# Concurrent uploads (requires GNU parallel)
echo "Concurrent uploads:"
time seq 1 $CONCURRENT_UPLOADS | parallel -j $CONCURRENT_UPLOADS \
  "curl -s -X POST '$BASE_URL/upload' -F 'file=@$TEST_FILE' -o /dev/null"
```

## Performance Tuning Checklist

### System Level
- [ ] Use SSD storage for local files
- [ ] Ensure adequate RAM (minimum 1GB recommended)
- [ ] Configure appropriate file system (ext4, XFS)
- [ ] Set proper ulimits for file descriptors
- [ ] Use dedicated network interface if possible

### Application Level
- [ ] Tune worker pool size based on CPU cores
- [ ] Configure memory limits appropriately
- [ ] Enable compression for network efficiency
- [ ] Set appropriate timeouts for operations
- [ ] Monitor and tune garbage collection

### Network Level
- [ ] Use HTTP/2 if supported by clients
- [ ] Configure proper TCP buffer sizes
- [ ] Enable keep-alive connections
- [ ] Use CDN for static content if applicable
- [ ] Monitor network latency and bandwidth

## Common Performance Issues

### 1. High QR Generation Latency

**Symptoms:**
- QR generation > 80ms consistently
- Timeout errors under load
- High CPU usage

**Solutions:**
```yaml
worker_pool:
  size: 20                    # Increase workers
  queue_size: 200             # Larger queue

performance:
  gc_interval_seconds: 15     # More frequent GC
```

### 2. Memory Pressure

**Symptoms:**
- Frequent garbage collection
- Out of memory errors
- Slow response times

**Solutions:**
```yaml
performance:
  memory_limit: 1073741824    # Increase limit
  force_gc_threshold: 268435456 # Lower GC threshold
  stream_buffer_size: 16384   # Smaller buffers
```

### 3. Upload Bottlenecks

**Symptoms:**
- Slow file uploads
- Upload timeouts
- High disk I/O wait

**Solutions:**
```yaml
storage:
  max_file_size: 5242880      # Reduce max size
  local_path: "/fast-storage" # Use faster storage

timeouts:
  file_upload: 120            # Increase timeout
```

### 4. Database/Metadata Performance

**Symptoms:**
- Slow image listing
- Cleanup operations timeout
- High metadata operation latency

**Solutions:**
- Use faster storage for metadata files
- Implement metadata caching
- Optimize cleanup batch sizes
- Consider database indexing for large datasets

## Monitoring and Alerting

### Key Metrics to Monitor
1. **Response Times**: 95th percentile < 80ms for QR generation
2. **Error Rates**: < 1% error rate under normal load
3. **Memory Usage**: < 80% of configured limit
4. **Worker Pool**: Queue size < 50% of capacity
5. **Disk Usage**: < 90% of available storage

### Recommended Monitoring Tools
- **Prometheus + Grafana**: For metrics collection and visualization
- **Application logs**: For detailed performance analysis
- **System monitoring**: CPU, memory, disk, network metrics
- **Health check monitoring**: Regular health endpoint polling

### Sample Grafana Queries
```promql
# Average QR generation time
avg(rate(qr_generation_duration_seconds_sum[5m]) / rate(qr_generation_duration_seconds_count[5m]))

# Error rate
rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])

# Memory usage
process_resident_memory_bytes / 1024 / 1024

# Worker pool utilization
worker_pool_active_workers / worker_pool_total_workers
```

## Performance Best Practices

### For Clients
1. **Reuse Connections**: Use HTTP keep-alive
2. **Batch Operations**: Group multiple requests when possible
3. **Appropriate Timeouts**: Set reasonable client timeouts
4. **Error Handling**: Implement proper retry logic
5. **Compression**: Accept gzip encoding

### For Deployment
1. **Resource Allocation**: Provide adequate CPU and memory
2. **Storage Performance**: Use fast storage for active data
3. **Network Configuration**: Optimize network settings
4. **Monitoring**: Implement comprehensive monitoring
5. **Scaling**: Plan for horizontal scaling if needed

### For Development
1. **Profile Regularly**: Use profiling tools to identify bottlenecks
2. **Load Testing**: Regular performance testing under realistic loads
3. **Memory Management**: Monitor memory usage patterns
4. **Optimization**: Continuously optimize based on metrics
5. **Documentation**: Keep performance documentation updated
