package compositor

import (
	"bytes"
	"fmt"
	"image"
	"image/draw"
	"image/jpeg"
	"image/png"
	"strings"
)

// Compositor implements the ImageCompositor interface
type Compositor struct{}

// NewCompositor creates a new Compositor instance
func NewCompositor() *Compositor {
	return &Compositor{}
}

// Composite combines a QR code with a background image at specified coordinates
func (c *Compositor) Composite(background image.Image, qr image.Image, x, y int) (image.Image, error) {
	// Validate positioning - ensure QR code fits within background boundaries
	if err := c.validatePositioning(background, qr, x, y); err != nil {
		return nil, err
	}

	// Create a new RGBA image with the same bounds as the background
	bounds := background.Bounds()
	composite := image.NewRGBA(bounds)

	// Draw the background image onto the composite
	draw.Draw(composite, bounds, background, bounds.Min, draw.Src)

	// Calculate the rectangle where the QR code will be placed
	qrBounds := qr.Bounds()
	qrRect := image.Rect(x, y, x+qrBounds.Dx(), y+qrBounds.Dy())

	// Draw the QR code onto the composite at the specified position
	draw.Draw(composite, qrRect, qr, qrBounds.Min, draw.Over)

	return composite, nil
}

// CompositeToBuffer combines images and writes the result directly to a buffer
func (c *Compositor) CompositeToBuffer(background image.Image, qr image.Image, x, y int, buf *bytes.Buffer, format string) error {
	// First create the composite image
	composite, err := c.Composite(background, qr, x, y)
	if err != nil {
		return err
	}

	// Encode the composite image to the buffer based on the specified format
	return c.encodeToBuffer(composite, buf, format)
}

// validatePositioning ensures the QR code fits within the background image boundaries
func (c *Compositor) validatePositioning(background image.Image, qr image.Image, x, y int) error {
	backgroundBounds := background.Bounds()
	qrBounds := qr.Bounds()

	// Check if starting position is within background
	if x < backgroundBounds.Min.X || y < backgroundBounds.Min.Y {
		return fmt.Errorf("QR code position (%d, %d) is outside background image bounds", x, y)
	}

	// Check if QR code extends beyond background boundaries
	qrWidth := qrBounds.Dx()
	qrHeight := qrBounds.Dy()
	backgroundWidth := backgroundBounds.Dx()
	backgroundHeight := backgroundBounds.Dy()

	if x+qrWidth > backgroundBounds.Min.X+backgroundWidth {
		return fmt.Errorf("QR code extends beyond background width: QR ends at %d, background width is %d", x+qrWidth, backgroundWidth)
	}

	if y+qrHeight > backgroundBounds.Min.Y+backgroundHeight {
		return fmt.Errorf("QR code extends beyond background height: QR ends at %d, background height is %d", y+qrHeight, backgroundHeight)
	}

	return nil
}

// encodeToBuffer encodes the image to the specified format and writes to buffer
func (c *Compositor) encodeToBuffer(img image.Image, buf *bytes.Buffer, format string) error {
	// Normalize format string
	format = strings.ToLower(strings.TrimSpace(format))

	switch format {
	case "jpeg", "jpg":
		return jpeg.Encode(buf, img, &jpeg.Options{Quality: 90})
	case "png":
		return png.Encode(buf, img)
	case "webp":
		// Note: golang.org/x/image/webp only supports decoding
		// For encoding WebP, we would need a different library
		// For now, fallback to PNG
		return png.Encode(buf, img)
	default:
		return fmt.Errorf("unsupported image format: %s. Supported formats: jpeg, jpg, png, webp", format)
	}
}