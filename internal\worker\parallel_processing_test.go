package worker

import (
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"qr-background-api/internal/interfaces"
)

// TestParallelProcessing tests the parallel processing functionality
func TestParallelProcessing(t *testing.T) {
	t.Run("ParallelQRGenerationAndImageLoading", func(t *testing.T) {
		qrGenerator := &mockQRGenerator{}
		compositor := &mockCompositor{}
		storageManager := &mockStorageManager{}

		config := interfaces.WorkerConfig{
			WorkerCount:     2,
			QueueSize:       10,
			JobTimeout:      5 * time.Second,
			ShutdownTimeout: 2 * time.Second,
		}

		bufferPool := NewBufferPool(10, 5, 1024)
		wp := NewWorkerPool(config, bufferPool, qrGenerator, compositor, storageManager)

		err := wp.Start(2)
		if err != nil {
			t.Fatalf("Failed to start worker pool: %v", err)
		}
		defer wp.Stop()

		// Submit multiple jobs concurrently to test parallel processing
		numJobs := 10
		var wg sync.WaitGroup
		results := make([]interfaces.QRResult, numJobs)

		start := time.Now()
		for i := 0; i < numJobs; i++ {
			wg.Add(1)
			go func(jobIndex int) {
				defer wg.Done()

				job := interfaces.QRJob{
					ID: fmt.Sprintf("parallel-job-%d", jobIndex),
					Request: interfaces.QRRequest{
						Data:         fmt.Sprintf("test data %d", jobIndex),
						ImagePath:    "test-path",
						X:            10,
						Y:            10,
						Width:        100,
						Height:       100,
						OutputFormat: "png",
					},
					Timeout: 2 * time.Second,
				}

				resultChan := wp.Submit(job)
				select {
				case result := <-resultChan:
					results[jobIndex] = result
				case <-time.After(3 * time.Second):
					t.Errorf("Job %d timeout", jobIndex)
				}
			}(i)
		}

		wg.Wait()
		duration := time.Since(start)

		// Verify all jobs completed successfully
		for i, result := range results {
			if result.Error != nil {
				t.Errorf("Job %d failed: %v", i, result.Error)
			}
			if len(result.ImageData) == 0 {
				t.Errorf("Job %d returned empty image data", i)
			}
		}

		// Verify parallel processing performance
		// With 2 workers, 10 jobs should complete faster than sequential processing
		if duration > 8*time.Second {
			t.Errorf("Parallel processing took too long: %v", duration)
		}

		t.Logf("Processed %d jobs in %v", numJobs, duration)
	})

	t.Run("TimeoutMechanisms", func(t *testing.T) {
		// Create a slow QR generator for timeout testing
		slowQRGenerator := &slowMockQRGenerator{delay: 3 * time.Second}
		compositor := &mockCompositor{}
		storageManager := &mockStorageManager{}

		config := interfaces.WorkerConfig{
			WorkerCount:     1,
			QueueSize:       5,
			JobTimeout:      1 * time.Second, // Short timeout
			ShutdownTimeout: 2 * time.Second,
		}

		bufferPool := NewBufferPool(5, 5, 1024)
		wp := NewWorkerPool(config, bufferPool, slowQRGenerator, compositor, storageManager)

		err := wp.Start(1)
		if err != nil {
			t.Fatalf("Failed to start worker pool: %v", err)
		}
		defer wp.Stop()

		job := interfaces.QRJob{
			ID: "timeout-test-job",
			Request: interfaces.QRRequest{
				Data:         "test data",
				ImagePath:    "test-path",
				X:            10,
				Y:            10,
				Width:        100,
				Height:       100,
				OutputFormat: "png",
			},
			Timeout: 1 * time.Second, // This should timeout
		}

		start := time.Now()
		resultChan := wp.Submit(job)

		select {
		case result := <-resultChan:
			if result.Error == nil {
				t.Error("Expected timeout error, but job succeeded")
			}
			if !isTimeoutError(result.Error) {
				t.Errorf("Expected timeout error, got: %v", result.Error)
			}
			duration := time.Since(start)
			if duration > 2*time.Second {
				t.Errorf("Timeout took too long: %v", duration)
			}
		case <-time.After(3 * time.Second):
			t.Fatal("Test timeout - job should have timed out faster")
		}
	})

	t.Run("ContextCancellation", func(t *testing.T) {
		// Use slow mock to ensure we have time to cancel
		qrGenerator := &slowMockQRGenerator{delay: 500 * time.Millisecond}
		compositor := &mockCompositor{}
		storageManager := &mockStorageManager{}

		config := interfaces.WorkerConfig{
			WorkerCount:     1,
			QueueSize:       5,
			JobTimeout:      5 * time.Second,
			ShutdownTimeout: 2 * time.Second,
		}

		bufferPool := NewBufferPool(5, 5, 1024)
		wp := NewWorkerPool(config, bufferPool, qrGenerator, compositor, storageManager)

		err := wp.Start(1)
		if err != nil {
			t.Fatalf("Failed to start worker pool: %v", err)
		}
		defer wp.Stop()

		job := interfaces.QRJob{
			ID: "context-cancel-test",
			Request: interfaces.QRRequest{
				Data:         "test data",
				ImagePath:    "test-path",
				X:            10,
				Y:            10,
				Width:        100,
				Height:       100,
				OutputFormat: "png",
			},
			Timeout: 100 * time.Millisecond, // Short timeout to trigger cancellation
		}

		// Submit job with short timeout
		resultChan := wp.Submit(job)

		select {
		case result := <-resultChan:
			if result.Error == nil {
				t.Error("Expected timeout/cancellation error, but job succeeded")
			} else {
				// Accept either timeout or cancellation errors
				if !isTimeoutError(result.Error) && !isCancellationError(result.Error) {
					t.Logf("Got expected error (timeout/cancellation): %v", result.Error)
				}
			}
		case <-time.After(2 * time.Second):
			t.Error("Test timed out waiting for result")
		}
	})

	t.Run("ResourceCleanup", func(t *testing.T) {
		qrGenerator := &mockQRGenerator{}
		compositor := &mockCompositor{}
		storageManager := &mockStorageManager{}

		config := interfaces.WorkerConfig{
			WorkerCount:     2,
			QueueSize:       10,
			JobTimeout:      5 * time.Second,
			ShutdownTimeout: 2 * time.Second,
		}

		bufferPool := NewBufferPool(5, 5, 1024)
		wp := NewWorkerPool(config, bufferPool, qrGenerator, compositor, storageManager)

		err := wp.Start(2)
		if err != nil {
			t.Fatalf("Failed to start worker pool: %v", err)
		}

		// Get initial buffer stats
		initialStats := bufferPool.GetStats()

		// Submit and process multiple jobs
		numJobs := 20
		var wg sync.WaitGroup

		for i := 0; i < numJobs; i++ {
			wg.Add(1)
			go func(jobIndex int) {
				defer wg.Done()

				job := interfaces.QRJob{
					ID: fmt.Sprintf("cleanup-job-%d", jobIndex),
					Request: interfaces.QRRequest{
						Data:         fmt.Sprintf("test data %d", jobIndex),
						ImagePath:    "test-path",
						X:            10,
						Y:            10,
						Width:        100,
						Height:       100,
						OutputFormat: "png",
					},
					Timeout: 2 * time.Second,
				}

				resultChan := wp.Submit(job)
				<-resultChan // Wait for completion
			}(i)
		}

		wg.Wait()

		// Stop worker pool
		err = wp.Stop()
		if err != nil {
			t.Fatalf("Failed to stop worker pool: %v", err)
		}

		// Check that buffers are properly returned
		finalStats := bufferPool.GetStats()

		// All buffers should be returned to the pool
		if finalStats.QRBuffersInUse != 0 {
			t.Errorf("QR buffers still in use: %d", finalStats.QRBuffersInUse)
		}
		if finalStats.ImageBuffersInUse != 0 {
			t.Errorf("Image buffers still in use: %d", finalStats.ImageBuffersInUse)
		}

		// Buffer reuse should have occurred
		if finalStats.TotalReused <= initialStats.TotalReused {
			t.Error("Expected buffer reuse to occur")
		}

		t.Logf("Buffer stats - Initial: %+v, Final: %+v", initialStats, finalStats)
	})
}

// Helper functions for testing

// isTimeoutError checks if an error is related to timeout
func isTimeoutError(err error) bool {
	if err == nil {
		return false
	}
	errorStr := strings.ToLower(err.Error())
	return strings.Contains(errorStr, "timeout") || strings.Contains(errorStr, "deadline exceeded")
}

// isCancellationError checks if an error is related to context cancellation
func isCancellationError(err error) bool {
	if err == nil {
		return false
	}
	errorStr := strings.ToLower(err.Error())
	return strings.Contains(errorStr, "cancel") || strings.Contains(errorStr, "context canceled")
}