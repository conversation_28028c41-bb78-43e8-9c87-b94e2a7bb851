package middleware

import (
	"bytes"
	"context"
	"net/http"
	"time"

	"github.com/google/uuid"
	"qr-background-api/internal/logging"
)

// ResponseWriter wraps http.ResponseWriter to capture response data
type ResponseWriter struct {
	http.ResponseWriter
	body       *bytes.Buffer
	statusCode int
	size       int64
}

// NewResponseWriter creates a new ResponseWriter
func NewResponseWriter(w http.ResponseWriter) *ResponseWriter {
	return &ResponseWriter{
		ResponseWriter: w,
		body:           &bytes.Buffer{},
		statusCode:     http.StatusOK,
	}
}

// Write captures the response body and size
func (rw *ResponseWriter) Write(b []byte) (int, error) {
	n, err := rw.ResponseWriter.Write(b)
	if err == nil {
		rw.body.Write(b)
		rw.size += int64(n)
	}
	return n, err
}

// WriteHeader captures the status code
func (rw *ResponseWriter) WriteHeader(statusCode int) {
	rw.statusCode = statusCode
	rw.ResponseWriter.WriteHeader(statusCode)
}

// Body returns the captured response body
func (rw *ResponseWriter) Body() []byte {
	return rw.body.Bytes()
}

// StatusCode returns the captured status code
func (rw *ResponseWriter) StatusCode() int {
	return rw.statusCode
}

// Size returns the response size in bytes
func (rw *ResponseWriter) Size() int64 {
	return rw.size
}

// LoggingMiddleware provides comprehensive request/response logging with metrics
func LoggingMiddleware(logger *logging.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			
			// Generate request ID if not present
			requestID := r.Header.Get("X-Request-ID")
			if requestID == "" {
				requestID = uuid.New().String()
			}
			
			// Add request ID to context and response header
			ctx := context.WithValue(r.Context(), "request_id", requestID)
			r = r.WithContext(ctx)
			w.Header().Set("X-Request-ID", requestID)
			
			// Capture request size
			requestSize := r.ContentLength
			if requestSize < 0 {
				requestSize = 0
			}
			
			// Log incoming request
			logger.WithFields(logging.Fields{
				"request_id":   requestID,
				"method":       r.Method,
				"path":         r.URL.Path,
				"query":        r.URL.RawQuery,
				"user_agent":   r.UserAgent(),
				"remote_addr":  r.RemoteAddr,
				"content_type": r.Header.Get("Content-Type"),
				"request_size": requestSize,
			}).Info("Incoming HTTP request")
			
			// Wrap response writer to capture response data
			rw := NewResponseWriter(w)
			
			// Process request
			next.ServeHTTP(rw, r)
			
			// Calculate duration
			duration := time.Since(start)
			
			// Create metrics
			metrics := &logging.RequestMetrics{
				Method:       r.Method,
				Path:         r.URL.Path,
				StatusCode:   rw.StatusCode(),
				Duration:     duration,
				RequestSize:  requestSize,
				ResponseSize: rw.Size(),
				UserAgent:    r.UserAgent(),
				RemoteAddr:   r.RemoteAddr,
				RequestID:    requestID,
			}
			
			// Add error information if status indicates error
			if rw.StatusCode() >= 400 {
				// Try to extract error information from response body
				responseBody := string(rw.Body())
				if len(responseBody) > 0 && len(responseBody) < 1000 {
					metrics.ErrorMessage = responseBody
				}
			}
			
			// Log request completion with metrics
			logger.LogRequest(metrics)
			
			// Log performance warning for slow requests
			if duration > 5*time.Second {
				logger.WithFields(logging.Fields{
					"request_id": requestID,
					"method":     r.Method,
					"path":       r.URL.Path,
					"duration":   duration.Milliseconds(),
				}).Warn("Slow request detected")
			}
		})
	}
}

// TimeoutMiddleware adds timeout to HTTP requests
func TimeoutMiddleware(timeout time.Duration) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Create context with timeout
			ctx, cancel := context.WithTimeout(r.Context(), timeout)
			defer cancel()
			
			// Replace request context
			r = r.WithContext(ctx)
			
			// Channel to signal completion
			done := make(chan struct{})
			
			// Run request in goroutine
			go func() {
				defer close(done)
				next.ServeHTTP(w, r)
			}()
			
			// Wait for completion or timeout
			select {
			case <-done:
				// Request completed normally
				return
			case <-ctx.Done():
				// Request timed out
				logger := logging.GetLogger()
				requestID, _ := r.Context().Value("request_id").(string)
				
				logger.WithFields(logging.Fields{
					"request_id": requestID,
					"method":     r.Method,
					"path":       r.URL.Path,
					"timeout":    timeout.String(),
				}).Error("Request timeout")
				
				// Send timeout response
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusRequestTimeout)
				w.Write([]byte(`{"success":false,"error":"Request timeout","code":408}`))
			}
		})
	}
}

// RecoveryMiddleware recovers from panics and logs them
func RecoveryMiddleware(logger *logging.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			defer func() {
				if err := recover(); err != nil {
					requestID, _ := r.Context().Value("request_id").(string)
					
					logger.WithFields(logging.Fields{
						"request_id": requestID,
						"method":     r.Method,
						"path":       r.URL.Path,
						"panic":      err,
					}).Error("Panic recovered in HTTP handler")
					
					// Send error response
					w.Header().Set("Content-Type", "application/json")
					w.WriteHeader(http.StatusInternalServerError)
					w.Write([]byte(`{"success":false,"error":"Internal server error","code":500}`))
				}
			}()
			
			next.ServeHTTP(w, r)
		})
	}
}

// RequestSizeLimitMiddleware limits the size of incoming requests
func RequestSizeLimitMiddleware(maxSize int64) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if r.ContentLength > maxSize {
				logger := logging.GetLogger()
				requestID, _ := r.Context().Value("request_id").(string)
				
				logger.WithFields(logging.Fields{
					"request_id":     requestID,
					"method":         r.Method,
					"path":           r.URL.Path,
					"content_length": r.ContentLength,
					"max_size":       maxSize,
				}).Warn("Request size limit exceeded")
				
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusRequestEntityTooLarge)
				w.Write([]byte(`{"success":false,"error":"Request entity too large","code":413}`))
				return
			}
			
			// Limit the request body reader
			r.Body = http.MaxBytesReader(w, r.Body, maxSize)
			
			next.ServeHTTP(w, r)
		})
	}
}

// GetRequestID extracts request ID from context
func GetRequestID(ctx context.Context) string {
	if requestID, ok := ctx.Value("request_id").(string); ok {
		return requestID
	}
	return ""
}

// SetRequestID sets request ID in context
func SetRequestID(ctx context.Context, requestID string) context.Context {
	return context.WithValue(ctx, "request_id", requestID)
}