# QR Generation Endpoint

Generate QR codes positioned on background images with custom positioning and dimensions.

## Endpoint Details

- **URL**: `/generate-qr`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Timeout**: 5 seconds
- **Performance**: < 80ms processing time requirement

## Request

### Headers
```http
Content-Type: application/json
Accept: application/json
```

### Body Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `data` | string | Yes | QR code content/data |
| `image_path` | string | Yes | Path to background image (from upload response) |
| `x` | integer | Yes | X coordinate for QR code position |
| `y` | integer | Yes | Y coordinate for QR code position |
| `width` | integer | Yes | QR code width in pixels |
| `height` | integer | Yes | QR code height in pixels |
| `output_format` | string | Yes | Output image format (`png`, `jpeg`, `gif`) |

### Request Example
```json
{
  "data": "https://example.com",
  "image_path": "images/347/abc123def456.jpg",
  "x": 100,
  "y": 100,
  "width": 200,
  "height": 200,
  "output_format": "png"
}
```

## Response

### Success Response (200 OK)
The response contains the generated image as binary data with appropriate headers:

```http
HTTP/1.1 200 OK
Content-Type: image/png
Content-Length: 45678
X-Processing-Time-Ms: 65
X-Request-ID: 123e4567-e89b-12d3-a456-426614174000

[Binary image data]
```

### Response Headers

| Header | Description |
|--------|-------------|
| `Content-Type` | MIME type of generated image |
| `Content-Length` | Size of image in bytes |
| `X-Processing-Time-Ms` | Processing time in milliseconds |
| `X-Request-ID` | Request tracking ID |

## Error Responses

### 400 Bad Request - Validation Failed
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "Request validation failed",
    "details": [
      {
        "field": "data",
        "message": "QR data cannot be empty",
        "value": ""
      },
      {
        "field": "width",
        "message": "QR code size must be between 64 and 2048 pixels",
        "value": 32
      }
    ]
  }
}
```

### 400 Bad Request - Invalid QR Size
```json
{
  "success": false,
  "error": {
    "code": "INVALID_QR_SIZE",
    "message": "QR code size must be between 64 and 2048 pixels",
    "details": {
      "provided_size": 32,
      "min_size": 64,
      "max_size": 2048
    }
  }
}
```

### 404 Not Found - Image Not Found
```json
{
  "success": false,
  "error": {
    "code": "IMAGE_NOT_FOUND",
    "message": "Background image not found",
    "details": {
      "image_path": "images/347/nonexistent.jpg"
    }
  }
}
```

### 400 Bad Request - QR Position Out of Bounds
```json
{
  "success": false,
  "error": {
    "code": "QR_POSITION_OUT_OF_BOUNDS",
    "message": "QR code position exceeds image boundaries",
    "details": {
      "qr_x": 1500,
      "qr_y": 1000,
      "qr_width": 200,
      "qr_height": 200,
      "image_width": 1200,
      "image_height": 800
    }
  }
}
```

### 408 Request Timeout - Processing Timeout
```json
{
  "success": false,
  "error": {
    "code": "QR_PROCESSING_TIMEOUT",
    "message": "QR generation processing timeout",
    "details": "Processing exceeded 80ms requirement"
  }
}
```

### 500 Internal Server Error - QR Generation Failed
```json
{
  "success": false,
  "error": {
    "code": "QR_GENERATION_FAILED",
    "message": "Failed to generate QR code",
    "details": "invalid QR data encoding"
  }
}
```

## Examples

### cURL Example
```bash
curl -X POST http://localhost:8080/generate-qr \
  -H "Content-Type: application/json" \
  -d '{
    "data": "https://example.com",
    "image_path": "images/347/abc123def456.jpg",
    "x": 100,
    "y": 100,
    "width": 200,
    "height": 200,
    "output_format": "png"
  }' \
  --output generated-qr.png
```

### JavaScript Example
```javascript
const qrRequest = {
  data: 'https://example.com',
  image_path: 'images/347/abc123def456.jpg',
  x: 100,
  y: 100,
  width: 200,
  height: 200,
  output_format: 'png'
};

fetch('http://localhost:8080/generate-qr', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(qrRequest)
})
.then(response => {
  if (response.ok) {
    return response.blob();
  } else {
    return response.json().then(err => Promise.reject(err));
  }
})
.then(blob => {
  // Create download link or display image
  const url = URL.createObjectURL(blob);
  const img = document.createElement('img');
  img.src = url;
  document.body.appendChild(img);
})
.catch(error => {
  console.error('QR generation failed:', error);
});
```

### Python Example
```python
import requests
import json

url = 'http://localhost:8080/generate-qr'
data = {
    'data': 'https://example.com',
    'image_path': 'images/347/abc123def456.jpg',
    'x': 100,
    'y': 100,
    'width': 200,
    'height': 200,
    'output_format': 'png'
}

response = requests.post(url, json=data)

if response.status_code == 200:
    # Save the generated image
    with open('generated-qr.png', 'wb') as f:
        f.write(response.content)
    print(f"QR code generated successfully")
    print(f"Processing time: {response.headers.get('X-Processing-Time-Ms')}ms")
else:
    error = response.json()
    print(f"QR generation failed: {error['error']['message']}")
```

## Validation Rules

### QR Data
- **Required**: Cannot be empty
- **Length**: No specific limit (limited by QR code capacity)
- **Content**: Any string content (URLs, text, JSON, etc.)

### Image Path
- **Required**: Must be a valid path from upload response
- **Format**: Relative path from storage root
- **Existence**: File must exist and be accessible

### Position and Size
- **X, Y Coordinates**: Must be non-negative integers
- **Width, Height**: Must be between 64 and 2048 pixels
- **Boundaries**: QR code must fit within background image dimensions

### Output Format
- **Supported**: `png`, `jpeg`, `gif`
- **Case**: Case-insensitive
- **Default**: No default (must be specified)

## Performance Characteristics

- **Processing Time**: < 80ms requirement
- **Concurrent Processing**: Up to 10 parallel jobs
- **Memory Usage**: Optimized with streaming
- **Worker Pool**: Queue-based job distribution

## Implementation Details

### Processing Flow
1. **Request Validation**: All parameters are validated
2. **Worker Assignment**: Job is queued to worker pool
3. **Parallel Processing**: QR generation and image loading happen concurrently
4. **Image Composition**: QR code is overlaid on background
5. **Response Streaming**: Result is streamed back to client

### QR Code Generation
- Uses medium error correction level
- Square QR codes (uses smaller of width/height)
- Black QR code on transparent background
- Positioned precisely at specified coordinates
